import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock React
vi.mock('react', () => ({
  default: {
    lazy: vi.fn(),
  },
  lazy: vi.fn(),
}));

import ComponentUtils from './component';
import React from 'react';

const mockReactLazy = vi.mocked(React.lazy);

describe('component utils', () => {
  let localStorageMock: {
    getItem: any;
    setItem: any;
    clear: any;
  };
  let mockFactory: any;
  let mockComponent: any;
  let originalLocation: any;

  beforeEach(() => {
    // Store original location
    originalLocation = window.location;

    // Mock localStorage
    localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      clear: vi.fn(),
    };

    // Mock window.location.reload
    Object.defineProperty(window, 'location', {
      value: {
        ...originalLocation,
        reload: vi.fn(),
      },
      writable: true,
      configurable: true,
    });

    // Mock global localStorage
    Object.defineProperty(global, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });

    // Create mock factory and component
    mockComponent = { default: 'MockComponent' };
    mockFactory = vi.fn();

    // Reset React.lazy mock to return a mock component
    mockReactLazy.mockReturnValue('MockLazyComponent' as any);

    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Restore original location
    Object.defineProperty(window, 'location', {
      value: originalLocation,
      writable: true,
      configurable: true,
    });
  });

  describe('lazy', () => {
    it('should exist as a function', () => {
      expect(typeof ComponentUtils.lazy).toBe('function');
    });

    it('should call React.lazy and return its result', () => {
      const result = ComponentUtils.lazy(mockFactory);

      expect(mockReactLazy).toHaveBeenCalled();
      expect(result).toBe('MockLazyComponent');
    });

    it('should successfully load component and set localStorage to false', async () => {
      // Setup: no previous refresh
      localStorageMock.getItem.mockReturnValue('false');
      mockFactory.mockResolvedValue(mockComponent);

      // Create lazy component - this calls React.lazy with enhanced factory
      ComponentUtils.lazy(mockFactory);

      // Get the factory function that was passed to React.lazy
      const enhancedFactory = mockReactLazy.mock.calls[0][0];

      // Call the enhanced factory function
      const result = await enhancedFactory();

      expect(mockFactory).toHaveBeenCalled();
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'page-has-been-force-refreshed',
        'false'
      );
      expect(result).toBe(mockComponent);
    });

    it('should handle null localStorage gracefully', async () => {
      // Setup: localStorage returns null
      localStorageMock.getItem.mockReturnValue(null);
      mockFactory.mockResolvedValue(mockComponent);

      // Create lazy component
      ComponentUtils.lazy(mockFactory);

      // Get and call the enhanced factory function
      const enhancedFactory = mockReactLazy.mock.calls[0][0];
      const result = await enhancedFactory();

      expect(localStorageMock.getItem).toHaveBeenCalledWith('page-has-been-force-refreshed');
      expect(result).toBe(mockComponent);
    });

    it('should reload page on first error and set localStorage to true', async () => {
      // Setup: no previous refresh, factory throws error
      localStorageMock.getItem.mockReturnValue('false');
      mockFactory.mockRejectedValue(new Error('Component load failed'));

      // Create lazy component
      ComponentUtils.lazy(mockFactory);

      // Get and call the enhanced factory function
      const enhancedFactory = mockReactLazy.mock.calls[0][0];
      const result = await enhancedFactory();

      expect(mockFactory).toHaveBeenCalled();
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'page-has-been-force-refreshed',
        'true'
      );
      expect(window.location.reload).toHaveBeenCalled();
      expect(result).toBeUndefined(); // reload returns undefined
    });

    it('should throw error on second failure (after page refresh)', async () => {
      // Setup: page has already been refreshed, factory throws error
      localStorageMock.getItem.mockReturnValue('true');
      const testError = new Error('Component load failed again');
      mockFactory.mockRejectedValue(testError);

      // Create lazy component
      ComponentUtils.lazy(mockFactory);

      // Get the enhanced factory function and expect it to throw
      const enhancedFactory = mockReactLazy.mock.calls[0][0];
      await expect(enhancedFactory()).rejects.toThrow('Component load failed again');

      expect(mockFactory).toHaveBeenCalled();
      expect(localStorageMock.setItem).not.toHaveBeenCalledWith(
        'page-has-been-force-refreshed',
        'true'
      );
      expect(window.location.reload).not.toHaveBeenCalled();
    });

    it('should handle invalid JSON in localStorage', async () => {
      // Setup: localStorage has invalid JSON
      localStorageMock.getItem.mockReturnValue('invalid-json');
      mockFactory.mockResolvedValue(mockComponent);

      // Create lazy component
      ComponentUtils.lazy(mockFactory);

      // Get the enhanced factory function
      const enhancedFactory = mockReactLazy.mock.calls[0][0];

      // Should throw when trying to parse invalid JSON
      await expect(enhancedFactory()).rejects.toThrow('Unexpected token');
    });

    it('should handle localStorage being undefined', async () => {
      // Setup: localStorage is undefined
      Object.defineProperty(global, 'localStorage', {
        value: undefined,
        writable: true,
      });
      mockFactory.mockResolvedValue(mockComponent);

      // Create lazy component
      ComponentUtils.lazy(mockFactory);

      // Get the enhanced factory function
      const enhancedFactory = mockReactLazy.mock.calls[0][0];

      // Should throw because localStorage.setItem is called even in success path when localStorage is undefined
      await expect(enhancedFactory()).rejects.toThrow('Cannot read properties of undefined');
    });

    it('should throw error when localStorage is undefined and error occurs', async () => {
      // Setup: localStorage is undefined, factory throws error
      Object.defineProperty(global, 'localStorage', {
        value: undefined,
        writable: true,
      });
      mockFactory.mockRejectedValue(new Error('Component load failed'));

      // Create lazy component
      ComponentUtils.lazy(mockFactory);

      // Get the enhanced factory function
      const enhancedFactory = mockReactLazy.mock.calls[0][0];

      // Should throw because localStorage.setItem is called when localStorage is undefined
      await expect(enhancedFactory()).rejects.toThrow();
    });
  });
});
