import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { useSearchParams } from 'react-router-dom';
import useSWRInfinite from 'swr/infinite';
import { useData } from './useData';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useSearchParams: vi.fn(),
}));

vi.mock('swr/infinite', () => ({
  default: vi.fn(),
}));

vi.mock('@/services/api', () => ({
  RecordAPI: {
    get: vi.fn(),
  },
}));

// Mock react hooks
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useCallback: vi.fn((fn) => fn),
    useMemo: vi.fn((fn) => fn()),
  };
});

const { RecordAPI } = await import('@/services/api');

describe('useData', () => {
  const mockSetSearchParams = vi.fn();
  const mockMutate = vi.fn();
  const mockSetSize = vi.fn();

  const mockSearchParams = new URLSearchParams();

  const defaultSwrReturn = {
    data: undefined,
    isLoading: false,
    isValidating: false,
    mutate: mockMutate,
    size: 1,
    setSize: mockSetSize,
    error: undefined,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup useSearchParams mock
    vi.mocked(useSearchParams).mockReturnValue([mockSearchParams, mockSetSearchParams]);

    // Setup useSWRInfinite mock
    vi.mocked(useSWRInfinite).mockReturnValue(defaultSwrReturn);

    // Setup RecordAPI mock
    vi.mocked(RecordAPI.get).mockResolvedValue({
      records: [
        { id: '1', name: 'Record 1' },
        { id: '2', name: 'Record 2' },
      ],
      totalRecordsCount: 100,
      views: [],
      object: {},
    } as any);

    // Reset search params
    mockSearchParams.delete('pageSize');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic functionality', () => {
    it('should return hook with correct properties', () => {
      const { result } = renderHook(() => useData('ws1', 'view1'));

      expect(result.current).toHaveProperty('records');
      expect(result.current).toHaveProperty('isLoading');
      expect(result.current).toHaveProperty('isValidating');
      expect(result.current).toHaveProperty('mutate');
      expect(result.current).toHaveProperty('size');
      expect(result.current).toHaveProperty('setSize');
      expect(result.current).toHaveProperty('totalRecords');
    });

    it('should use default limit when no pageSize param', () => {
      renderHook(() => useData('ws1', 'view1'));

      expect(useSWRInfinite).toHaveBeenCalled();

      // Check that the getKey function generates correct cache key with default limit
      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toEqual(['ws1', 'view1', 0, 70, '', '', '']);
    });

    it('should use custom default limit', () => {
      renderHook(() => useData('ws1', 'view1', null, 50));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toEqual(['ws1', 'view1', 0, 50, '', '', '']);
    });

    it('should use pageSize from search params', () => {
      mockSearchParams.set('pageSize', '25');

      renderHook(() => useData('ws1', 'view1'));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toEqual(['ws1', 'view1', 0, 25, '', '', '']);
    });
  });

  describe('Cache key generation', () => {
    it('should return null when wsId is missing', () => {
      renderHook(() => useData(null, 'view1'));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeNull();
    });

    it('should return null when viewId is missing', () => {
      renderHook(() => useData('ws1', null));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeNull();
    });

    it('should return null when previous page has no records (pagination end)', () => {
      renderHook(() => useData('ws1', 'view1'));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(1, { records: [] });

      expect(key).toBeNull();
    });

    it('should include textSearch in cache key', () => {
      renderHook(() => useData('ws1', 'view1', 'search term'));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toEqual(['ws1', 'view1', 0, 70, 'search term', '', '']);
    });

    it('should handle null textSearch', () => {
      renderHook(() => useData('ws1', 'view1', null));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toEqual(['ws1', 'view1', 0, 70, '', '', '']);
    });

    it('should include view config filters in cache key', () => {
      const viewConfig = {
        filters: [{ field: 'name', operator: 'eq', value: 'test' }],
      };

      renderHook(() => useData('ws1', 'view1', null, 70, viewConfig));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeTruthy();
      expect(key![5]).toBe(JSON.stringify(viewConfig.filters));
    });

    it('should include view config sorts in cache key', () => {
      const viewConfig = {
        sort: [{ field: 'createdAt', direction: 'desc' }],
      };

      renderHook(() => useData('ws1', 'view1', null, 70, viewConfig));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeTruthy();
      expect(key![6]).toBe(JSON.stringify(viewConfig.sort));
    });

    it('should handle view config with both filters and sorts', () => {
      const viewConfig = {
        filters: [{ field: 'status', operator: 'eq', value: 'active' }],
        sort: [{ field: 'name', direction: 'asc' }],
      };

      renderHook(() => useData('ws1', 'view1', null, 70, viewConfig));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeTruthy();
      expect(key![5]).toBe(JSON.stringify(viewConfig.filters));
      expect(key![6]).toBe(JSON.stringify(viewConfig.sort));
    });

    it('should calculate correct offset for different page indexes', () => {
      renderHook(() => useData('ws1', 'view1', null, 50));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];

      expect(getKeyFn(0, null)).toEqual(['ws1', 'view1', 0, 50, '', '', '']);
      expect(getKeyFn(1, { records: ['dummy'] })).toEqual(['ws1', 'view1', 50, 50, '', '', '']);
      expect(getKeyFn(2, { records: ['dummy'] })).toEqual(['ws1', 'view1', 100, 50, '', '', '']);
    });
  });

  describe('SWR configuration', () => {
    it('should call useSWRInfinite with correct options', () => {
      renderHook(() => useData('ws1', 'view1'));

      expect(useSWRInfinite).toHaveBeenCalledWith(expect.any(Function), expect.any(Function), {
        revalidateIfStale: true,
        keepPreviousData: true,
        dedupingInterval: 2000,
        revalidateFirstPage: false,
        revalidateOnFocus: false,
      });
    });

    it('should call RecordAPI.get with correct parameters', async () => {
      renderHook(() => useData('ws1', 'view1', 'search', 25));

      const [, fetcherFn] = vi.mocked(useSWRInfinite).mock.calls[0];

      if (fetcherFn) {
        await fetcherFn(['ws1', 'view1', 0, 25, 'search']);
        expect(RecordAPI.get).toHaveBeenCalledWith('ws1', 'view1', 'search', 0, 25);
      }
    });

    it('should transform API response correctly', async () => {
      const mockApiResponse = {
        records: [{ id: '1', name: 'Test' }],
        totalRecordsCount: 50,
        other: 'ignored',
        views: [],
        object: {},
      } as any;

      vi.mocked(RecordAPI.get).mockResolvedValue(mockApiResponse);

      renderHook(() => useData('ws1', 'view1'));

      const [, fetcherFn] = vi.mocked(useSWRInfinite).mock.calls[0];

      if (fetcherFn) {
        const result = await fetcherFn(['ws1', 'view1', 0, 70, '']);

        expect(result).toEqual({
          records: [{ id: '1', name: 'Test' }],
          totalRecordsCount: 50,
        });
      }
    });
  });

  describe('Return value processing', () => {
    it('should flatten records from multiple pages', () => {
      vi.mocked(useSWRInfinite).mockReturnValue({
        ...defaultSwrReturn,
        data: [
          { records: [{ id: '1' }, { id: '2' }], totalRecordsCount: 100 },
          { records: [{ id: '3' }, { id: '4' }], totalRecordsCount: 100 },
        ],
      });

      const { result } = renderHook(() => useData('ws1', 'view1'));

      expect(result.current.records).toEqual([{ id: '1' }, { id: '2' }, { id: '3' }, { id: '4' }]);
    });

    it('should handle empty records in pages', () => {
      vi.mocked(useSWRInfinite).mockReturnValue({
        ...defaultSwrReturn,
        data: [
          { records: [{ id: '1' }], totalRecordsCount: 100 },
          { records: [], totalRecordsCount: 100 },
          { records: [{ id: '2' }], totalRecordsCount: 100 },
        ],
      });

      const { result } = renderHook(() => useData('ws1', 'view1'));

      expect(result.current.records).toEqual([{ id: '1' }, { id: '2' }]);
    });

    it('should handle pages with null/undefined records', () => {
      vi.mocked(useSWRInfinite).mockReturnValue({
        ...defaultSwrReturn,
        data: [
          { records: [{ id: '1' }], totalRecordsCount: 100 },
          { records: null, totalRecordsCount: 100 },
          { records: undefined, totalRecordsCount: 100 },
        ],
      });

      const { result } = renderHook(() => useData('ws1', 'view1'));

      expect(result.current.records).toEqual([{ id: '1' }]);
    });

    it('should return totalRecords from first page', () => {
      vi.mocked(useSWRInfinite).mockReturnValue({
        ...defaultSwrReturn,
        data: [
          { records: [{ id: '1' }], totalRecordsCount: 150 },
          { records: [{ id: '2' }], totalRecordsCount: 150 },
        ],
      });

      const { result } = renderHook(() => useData('ws1', 'view1'));

      expect(result.current.totalRecords).toBe(150);
    });

    it('should default totalRecords to 0 when no data', () => {
      vi.mocked(useSWRInfinite).mockReturnValue({
        ...defaultSwrReturn,
        data: undefined,
      });

      const { result } = renderHook(() => useData('ws1', 'view1'));

      expect(result.current.totalRecords).toBe(0);
    });

    it('should pass through SWR state correctly', () => {
      vi.mocked(useSWRInfinite).mockReturnValue({
        ...defaultSwrReturn,
        isLoading: true,
        isValidating: true,
        size: 3,
      });

      const { result } = renderHook(() => useData('ws1', 'view1'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.isValidating).toBe(true);
      expect(result.current.size).toBe(3);
      expect(result.current.mutate).toBe(mockMutate);
      expect(result.current.setSize).toBe(mockSetSize);
    });

    it('should handle empty data array', () => {
      vi.mocked(useSWRInfinite).mockReturnValue({
        ...defaultSwrReturn,
        data: [],
      });

      const { result } = renderHook(() => useData('ws1', 'view1'));

      expect(result.current.records).toEqual([]);
      expect(result.current.totalRecords).toBe(0);
    });
  });

  describe('Edge cases', () => {
    it('should handle invalid pageSize param', () => {
      mockSearchParams.set('pageSize', 'invalid');

      renderHook(() => useData('ws1', 'view1'));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      // Invalid pageSize results in NaN, which becomes offset 0 * NaN = NaN
      expect(key).toEqual(['ws1', 'view1', NaN, NaN, '', '', '']);
    });

    it('should handle zero pageSize param', () => {
      mockSearchParams.set('pageSize', '0');

      renderHook(() => useData('ws1', 'view1'));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toEqual(['ws1', 'view1', 0, 0, '', '', '']);
    });

    it('should handle negative pageSize param', () => {
      mockSearchParams.set('pageSize', '-10');

      renderHook(() => useData('ws1', 'view1'));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      // pageIndex (0) * limit (-10) = -0 (negative zero)
      expect(key).toEqual(['ws1', 'view1', -0, -10, '', '', '']);
    });

    it('should handle undefined wsId and viewId', () => {
      renderHook(() => useData(undefined, undefined));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeNull();
    });

    it('should handle empty string wsId and viewId', () => {
      renderHook(() => useData('', ''));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeNull();
    });

    it('should handle complex view config with nested objects', () => {
      const complexViewConfig = {
        filters: [
          {
            field: 'nested.field',
            operator: 'in',
            value: ['a', 'b', 'c'],
            config: { caseSensitive: true },
          },
        ],
        sort: [{ field: 'date', direction: 'desc', nullsLast: true }],
      };

      renderHook(() => useData('ws1', 'view1', null, 70, complexViewConfig));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeTruthy();
      expect(key![5]).toBe(JSON.stringify(complexViewConfig.filters));
      expect(key![6]).toBe(JSON.stringify(complexViewConfig.sort));
    });

    it('should handle view config with only filters', () => {
      const viewConfig = {
        filters: [{ field: 'status', operator: 'eq', value: 'active' }],
      };

      renderHook(() => useData('ws1', 'view1', null, 70, viewConfig));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeTruthy();
      expect(key![5]).toBe(JSON.stringify(viewConfig.filters));
      expect(key![6]).toBe('');
    });

    it('should handle view config with only sorts', () => {
      const viewConfig = {
        sort: [{ field: 'name', direction: 'asc' }],
      };

      renderHook(() => useData('ws1', 'view1', null, 70, viewConfig));

      const [getKeyFn] = vi.mocked(useSWRInfinite).mock.calls[0];
      const key = getKeyFn(0, null);

      expect(key).toBeTruthy();
      expect(key![5]).toBe('');
      expect(key![6]).toBe(JSON.stringify(viewConfig.sort));
    });

    it('should handle API errors gracefully', async () => {
      vi.mocked(RecordAPI.get).mockRejectedValue(new Error('API Error'));

      renderHook(() => useData('ws1', 'view1'));

      const [, fetcherFn] = vi.mocked(useSWRInfinite).mock.calls[0];

      if (fetcherFn) {
        await expect(fetcherFn(['ws1', 'view1', 0, 70, ''])).rejects.toThrow('API Error');
      }
    });
  });

  describe('Memoization and performance', () => {
    it('should be stable when props do not change', () => {
      const { result, rerender } = renderHook(({ wsId, viewId }) => useData(wsId, viewId), {
        initialProps: { wsId: 'ws1', viewId: 'view1' },
      });

      // Rerender with same props
      rerender({ wsId: 'ws1', viewId: 'view1' });

      // Test basic functionality rather than object identity
      expect(result.current.records).toEqual([]);
      expect(result.current.totalRecords).toBe(0);
      expect(typeof result.current.mutate).toBe('function');
      expect(typeof result.current.setSize).toBe('function');
    });

    it('should return different data when workspace changes', () => {
      const { result, rerender } = renderHook(({ wsId, viewId }) => useData(wsId, viewId), {
        initialProps: { wsId: 'ws1', viewId: 'view1' },
      });

      // Rerender with different workspace ID
      rerender({ wsId: 'ws2', viewId: 'view1' });

      // Should still return valid hook result
      expect(result.current).toHaveProperty('records');
      expect(result.current).toHaveProperty('totalRecords');
      expect(result.current).toHaveProperty('mutate');
      expect(result.current).toHaveProperty('setSize');
    });
  });
});
