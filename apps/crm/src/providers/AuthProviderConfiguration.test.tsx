import { renderWithMantine } from '@/tests/utils/testUtils';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@auth0/auth0-react', () => ({
  Auth0Provider: vi.fn(({ children }) => <div data-testid='auth0-provider'>{children}</div>),
}));

vi.mock('@/utils', () => ({
  getOrganizationName: vi.fn(),
  getRedirectUri: vi.fn(),
  isExcludedBasePathDomains: vi.fn(),
  isIncludedBasePathDomains: vi.fn(),
}));

vi.mock('../configs', () => ({
  default: {
    AUTH0: {
      DOMAIN: 'test-domain.auth0.com',
      CLIENT_ID: 'test-client-id',
      AUDIENCE: 'test-audience',
      SCOPE: 'openid profile email',
    },
    BASE_PATH: '/crm',
  },
}));

import AuthProviderConfiguration from './AuthProviderConfiguration';
import {
  getOrganizationName,
  getRedirectUri,
  isExcludedBasePathDomains,
  isIncludedBasePathDomains,
} from '@/utils';
import { Auth0Provider } from '@auth0/auth0-react';

const mockGetOrganizationName = vi.mocked(getOrganizationName);
const mockGetRedirectUri = vi.mocked(getRedirectUri);
const mockIsExcludedBasePathDomains = vi.mocked(isExcludedBasePathDomains);
const mockIsIncludedBasePathDomains = vi.mocked(isIncludedBasePathDomains);
const mockAuth0Provider = vi.mocked(Auth0Provider);

describe('AuthProviderConfiguration', () => {
  let originalLocation: Location;

  beforeEach(() => {
    // Store original location
    originalLocation = window.location;

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        href: 'https://test.example.com',
        origin: 'https://test.example.com',
      },
      writable: true,
      configurable: true,
    });

    // Reset mocks
    vi.clearAllMocks();

    // Set default mock returns
    mockGetRedirectUri.mockReturnValue('https://test.example.com/callback');
    mockGetOrganizationName.mockReturnValue('test-org');
    mockIsExcludedBasePathDomains.mockReturnValue(false);
    mockIsIncludedBasePathDomains.mockReturnValue(false);
  });

  afterEach(() => {
    // Restore original location
    Object.defineProperty(window, 'location', {
      value: originalLocation,
      writable: true,
      configurable: true,
    });
  });

  it('should render Auth0Provider with correct props when all config is present', () => {
    const children = <div>Test Children</div>;

    renderWithMantine(<AuthProviderConfiguration>{children}</AuthProviderConfiguration>);

    expect(mockAuth0Provider).toHaveBeenCalledWith(
      {
        domain: 'test-domain.auth0.com',
        clientId: 'test-client-id',
        onRedirectCallback: expect.any(Function),
        authorizationParams: {
          redirect_uri: 'https://test.example.com/callback',
          audience: 'test-audience',
          scope: 'openid profile email',
          organization: 'test-org',
        },
        children,
      },
      {}
    );
  });

  it('should return null when redirectUri is missing', () => {
    (mockGetRedirectUri as any).mockReturnValue(null);

    const { container } = renderWithMantine(
      <AuthProviderConfiguration>test</AuthProviderConfiguration>
    );

    // Check that no Auth0Provider was rendered by looking for the test-id
    expect(container.querySelector('[data-testid="auth0-provider"]')).toBeNull();
    expect(mockAuth0Provider).not.toHaveBeenCalled();
  });

  describe('onRedirectCallback', () => {
    let onRedirectCallback: (appState?: any) => void;

    beforeEach(() => {
      renderWithMantine(<AuthProviderConfiguration>test</AuthProviderConfiguration>);
      const providerProps = mockAuth0Provider.mock.calls[0][0];
      onRedirectCallback = providerProps.onRedirectCallback as (appState?: any) => void;
    });

    it('should redirect to origin when isExcludedBasePathDomains returns true', () => {
      mockIsExcludedBasePathDomains.mockReturnValue(true);

      onRedirectCallback();

      expect(window.location.href).toBe('https://test.example.com');
      expect(mockIsIncludedBasePathDomains).not.toHaveBeenCalled();
    });

    it('should redirect to origin + BASE_PATH when isIncludedBasePathDomains returns true', () => {
      mockIsExcludedBasePathDomains.mockReturnValue(false);
      mockIsIncludedBasePathDomains.mockReturnValue(true);

      onRedirectCallback();

      expect(window.location.href).toBe('https://test.example.com/crm');
    });

    it('should redirect to appState.returnTo when provided and no domain exclusions', () => {
      mockIsExcludedBasePathDomains.mockReturnValue(false);
      mockIsIncludedBasePathDomains.mockReturnValue(false);

      onRedirectCallback({ returnTo: '/custom-path' });

      expect(window.location.href).toBe('/custom-path');
    });

    it('should redirect to BASE_PATH when no appState and no domain exclusions', () => {
      mockIsExcludedBasePathDomains.mockReturnValue(false);
      mockIsIncludedBasePathDomains.mockReturnValue(false);

      onRedirectCallback();

      expect(window.location.href).toBe('/crm');
    });

    it('should redirect to BASE_PATH when appState is undefined and no domain exclusions', () => {
      mockIsExcludedBasePathDomains.mockReturnValue(false);
      mockIsIncludedBasePathDomains.mockReturnValue(false);

      onRedirectCallback(undefined);

      expect(window.location.href).toBe('/crm');
    });
  });

  it('should call utility functions for configuration', () => {
    renderWithMantine(<AuthProviderConfiguration>test</AuthProviderConfiguration>);

    expect(mockGetRedirectUri).toHaveBeenCalled();
    expect(mockGetOrganizationName).toHaveBeenCalled();
  });

  it('should handle null organization name', () => {
    mockGetOrganizationName.mockReturnValue(null as any);

    renderWithMantine(<AuthProviderConfiguration>test</AuthProviderConfiguration>);

    expect(mockAuth0Provider).toHaveBeenCalledWith(
      {
        domain: 'test-domain.auth0.com',
        clientId: 'test-client-id',
        onRedirectCallback: expect.any(Function),
        authorizationParams: {
          redirect_uri: 'https://test.example.com/callback',
          audience: 'test-audience',
          scope: 'openid profile email',
          organization: null,
        },
        children: 'test',
      },
      {}
    );
  });

  it('should handle undefined organization name', () => {
    mockGetOrganizationName.mockReturnValue(undefined);

    renderWithMantine(<AuthProviderConfiguration>test</AuthProviderConfiguration>);

    expect(mockAuth0Provider).toHaveBeenCalledWith(
      {
        domain: 'test-domain.auth0.com',
        clientId: 'test-client-id',
        onRedirectCallback: expect.any(Function),
        authorizationParams: {
          redirect_uri: 'https://test.example.com/callback',
          audience: 'test-audience',
          scope: 'openid profile email',
          organization: undefined,
        },
        children: 'test',
      },
      {}
    );
  });
});
