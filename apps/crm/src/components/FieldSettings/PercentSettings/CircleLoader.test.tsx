import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { CircleLoader } from './CircleLoader';

describe('CircleLoader', () => {
  it('should render SVG element with correct dimensions', () => {
    const { container } = render(<CircleLoader />);

    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute('width', '18');
    expect(svg).toHaveAttribute('height', '18');
    expect(svg).toHaveAttribute('viewBox', '0 0 18 18');
  });

  it('should render with correct namespace and fill attributes', () => {
    const { container } = render(<CircleLoader />);

    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('fill', 'none');
    expect(svg).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
  });

  it('should contain two path elements for the circle', () => {
    const { container } = render(<CircleLoader />);

    const paths = container.querySelectorAll('path');
    expect(paths).toHaveLength(2);
  });

  it('should have background circle path with correct attributes', () => {
    const { container } = render(<CircleLoader />);

    const paths = container.querySelectorAll('path');
    const backgroundPath = paths[0];

    expect(backgroundPath).toHaveAttribute('fill', '#8485C7');

    const pathData = backgroundPath.getAttribute('d');
    expect(pathData).toBe(
      'M18 9C18 13.9706 13.9706 18 9 18C4.02944 18 0 13.9706 0 9C0 4.02944 4.02944 0 9 0C13.9706 0 18 4.02944 18 9ZM2.5704 9C2.5704 12.551 5.44903 15.4296 9 15.4296C12.551 15.4296 15.4296 12.551 15.4296 9C15.4296 5.44903 12.551 2.5704 9 2.5704C5.44903 2.5704 2.5704 5.44903 2.5704 9Z'
    );
  });

  it('should have progress arc path with correct attributes', () => {
    const { container } = render(<CircleLoader />);

    const paths = container.querySelectorAll('path');
    const progressPath = paths[1];

    expect(progressPath).toHaveAttribute('fill', '#3539BC');

    const pathData = progressPath.getAttribute('d');
    expect(pathData).toBe(
      'M9 -3.93402e-07C10.1819 -4.45065e-07 11.3522 0.232791 12.4442 0.685083C13.5361 1.13738 14.5282 1.80031 15.364 2.63604C16.1997 3.47177 16.8626 4.46392 17.3149 5.55585C17.7672 6.64778 18 7.8181 18 9L15.4296 9C15.4296 8.15565 15.2633 7.31957 14.9402 6.5395C14.6171 5.75942 14.1435 5.05063 13.5464 4.45359C12.9494 3.85654 12.2406 3.38294 11.4605 3.05982C10.6804 2.73671 9.84435 2.5704 9 2.5704L9 -3.93402e-07Z'
    );
  });

  it('should render without any errors', () => {
    expect(() => {
      render(<CircleLoader />);
    }).not.toThrow();
  });

  it('should be a pure functional component', () => {
    const { container: container1 } = render(<CircleLoader />);
    const { container: container2 } = render(<CircleLoader />);

    // Both renders should produce identical output
    expect(container1.innerHTML).toBe(container2.innerHTML);
  });

  it('should have correct color scheme', () => {
    const { container } = render(<CircleLoader />);

    const paths = container.querySelectorAll('path');

    // Background circle should be light purple
    expect(paths[0]).toHaveAttribute('fill', '#8485C7');

    // Progress arc should be dark blue
    expect(paths[1]).toHaveAttribute('fill', '#3539BC');
  });

  it('should maintain square aspect ratio', () => {
    const { container } = render(<CircleLoader />);

    const svg = container.querySelector('svg');
    const width = Number(svg?.getAttribute('width'));
    const height = Number(svg?.getAttribute('height'));

    // Aspect ratio should be 1:1 (18:18)
    expect(width / height).toBe(1);
  });

  it('should represent a circle with 18x18 dimensions', () => {
    const { container } = render(<CircleLoader />);

    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '18');
    expect(svg).toHaveAttribute('height', '18');
    expect(svg).toHaveAttribute('viewBox', '0 0 18 18');
  });

  describe('SVG structure validation', () => {
    it('should have exactly one SVG element', () => {
      const { container } = render(<CircleLoader />);

      const svgs = container.querySelectorAll('svg');
      expect(svgs).toHaveLength(1);
    });

    it('should have exactly two path elements as direct children', () => {
      const { container } = render(<CircleLoader />);

      const svg = container.querySelector('svg');
      const directChildren = svg?.children;
      expect(directChildren).toHaveLength(2);

      // Both children should be path elements
      const paths = svg?.querySelectorAll(':scope > path');
      expect(paths).toHaveLength(2);
    });

    it('should not contain any other SVG elements', () => {
      const { container } = render(<CircleLoader />);

      // Should not contain circles, rects, groups, etc.
      expect(container.querySelector('circle')).toBeNull();
      expect(container.querySelector('rect')).toBeNull();
      expect(container.querySelector('g')).toBeNull();
      expect(container.querySelector('defs')).toBeNull();
    });
  });

  describe('path data validation', () => {
    it('should have valid SVG path data for background circle', () => {
      const { container } = render(<CircleLoader />);

      const backgroundPath = container.querySelectorAll('path')[0];
      const pathData = backgroundPath.getAttribute('d');

      // Should start with M (moveTo) and contain C (curveTo) commands for circle
      expect(pathData).toMatch(/^M/);
      expect(pathData).toContain('C');
      expect(pathData).toContain('Z'); // Should close the path
    });

    it('should have valid SVG path data for progress arc', () => {
      const { container } = render(<CircleLoader />);

      const progressPath = container.querySelectorAll('path')[1];
      const pathData = progressPath.getAttribute('d');

      // Should start with M (moveTo) and contain C (curveTo) commands for arc
      expect(pathData).toMatch(/^M/);
      expect(pathData).toContain('C');
      expect(pathData).toContain('L'); // Should contain line commands
    });

    it('should represent circular geometry with center at (9,9)', () => {
      const { container } = render(<CircleLoader />);

      const paths = container.querySelectorAll('path');

      // Both paths should reference the center point (9,9) in their data
      paths.forEach((path) => {
        const pathData = path.getAttribute('d') || '';
        // Should contain coordinates that reference center at 9,9
        expect(pathData).toContain('9');
      });
    });
  });

  describe('accessibility and semantics', () => {
    it('should be focusable as a visual element', () => {
      const { container } = render(<CircleLoader />);

      const svg = container.querySelector('svg');
      // SVG should be in the document and accessible
      expect(svg).toBeInTheDocument();
    });

    it('should maintain consistent visual presentation', () => {
      const { container } = render(<CircleLoader />);

      // Should render the same way every time (no random elements)
      const paths = container.querySelectorAll('path');
      expect(paths[0].getAttribute('d')).toBeTruthy();
      expect(paths[1].getAttribute('d')).toBeTruthy();

      // Should have consistent colors
      expect(paths[0].getAttribute('fill')).toBe('#8485C7');
      expect(paths[1].getAttribute('fill')).toBe('#3539BC');
    });
  });
});
