import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { usePercentage } from './usePercentage';

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  Flex: ({ children, justify, w, align }: any) => (
    <div data-testid='flex' data-justify={justify} data-w={w} data-align={align}>
      {children}
    </div>
  ),
  Text: ({ children, c }: any) => (
    <span data-testid='text' data-color={c}>
      {children}
    </span>
  ),
}));

// Mock the loader components
vi.mock('./BarLoader', () => ({
  BarLoader: () => <div data-testid='bar-loader'>Bar Loader</div>,
}));

vi.mock('./CircleLoader', () => ({
  CircleLoader: () => <div data-testid='circle-loader'>Circle Loader</div>,
}));

// Mock translation hook
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: vi.fn((key: string) => {
      const translations: Record<string, string> = {
        ring: 'Ring',
        bar: 'Bar',
      };
      return translations[key] || key;
    }),
  })),
}));

describe('usePercentage', () => {
  it('should return both decimal places and display options', () => {
    const { result } = renderHook(() => usePercentage());

    expect(result.current).toHaveProperty('decimalPlaces');
    expect(result.current).toHaveProperty('displayOptions');
  });

  describe('decimalPlaces', () => {
    it('should generate correct number of decimal place options', () => {
      const { result } = renderHook(() => usePercentage());

      const decimalPlaces = result.current.decimalPlaces;
      expect(decimalPlaces).toHaveLength(1);
      expect(decimalPlaces[0].options).toHaveLength(5); // 1 to 5 decimal places
    });

    it('should generate correct option values and labels for decimal places', () => {
      const { result } = renderHook(() => usePercentage());

      const options = result.current.decimalPlaces[0].options;

      // Test first option (1 decimal place)
      expect(options[0].value).toBe(1);
      expect(options[0].selectedDisplay).toBe('1 (1.0 %)');

      // Test middle option (3 decimal places)
      expect(options[2].value).toBe(3);
      expect(options[2].selectedDisplay).toBe('3 (1.000 %)');

      // Test last option (5 decimal places)
      expect(options[4].value).toBe(5);
      expect(options[4].selectedDisplay).toBe('5 (1.00000 %)');
    });

    it('should format percentage decimal numbers correctly', () => {
      const { result } = renderHook(() => usePercentage());

      const options = result.current.decimalPlaces[0].options;

      // Verify all options include % symbol and proper decimal formatting
      options.forEach((option, index) => {
        const expectedDecimalPlaces = index + 1;
        expect(option.selectedDisplay).toContain('%');
        expect(option.selectedDisplay).toContain(`${expectedDecimalPlaces} (`);

        if (expectedDecimalPlaces === 1) {
          expect(option.selectedDisplay).toContain('1.0 %');
        } else {
          const expectedFormat = '1.' + '0'.repeat(expectedDecimalPlaces);
          expect(option.selectedDisplay).toContain(`${expectedFormat} %`);
        }
      });
    });

    it('should render Flex components for decimal place option labels', () => {
      const { result } = renderHook(() => usePercentage());

      const options = result.current.decimalPlaces[0].options;
      const firstOption = options[0];

      // Check that the label is a React element
      expect(firstOption.label).toBeDefined();
      expect(typeof firstOption.label).toBe('object');
    });
  });

  describe('displayOptions', () => {
    it('should return ring and bar display options', () => {
      const { result } = renderHook(() => usePercentage());

      const displayOptions = result.current.displayOptions;
      expect(displayOptions).toHaveLength(1);
      expect(displayOptions[0].options).toHaveLength(2);

      const options = displayOptions[0].options;
      expect(options[0].value).toBe('ring');
      expect(options[1].value).toBe('bar');
    });

    it('should render proper loader components for display options', () => {
      const { result } = renderHook(() => usePercentage());

      const options = result.current.displayOptions[0].options;

      // Check that the labels contain the loader components
      expect(options[0].label).toBeDefined();
      expect(options[1].label).toBeDefined();
      expect(typeof options[0].label).toBe('object');
      expect(typeof options[1].label).toBe('object');
    });

    it('should use translated labels for display options', () => {
      const { result } = renderHook(() => usePercentage());

      // We can't easily test the rendered content of React elements in this setup,
      // but we can verify the structure is correct
      const options = result.current.displayOptions[0].options;

      expect(options[0].value).toBe('ring');
      expect(options[1].value).toBe('bar');
    });
  });

  describe('memoization', () => {
    it('should maintain stable references for decimal places', () => {
      const { result, rerender } = renderHook(() => usePercentage());

      const firstRender = result.current.decimalPlaces;
      rerender();
      const secondRender = result.current.decimalPlaces;

      expect(firstRender).toBe(secondRender);
    });

    it('should maintain stable references for display options', () => {
      const { result, rerender } = renderHook(() => usePercentage());

      const firstRender = result.current.displayOptions;
      rerender();
      const secondRender = result.current.displayOptions;

      expect(firstRender).toBe(secondRender);
    });

    it('should maintain stable references for the entire hook result', () => {
      const { result, rerender } = renderHook(() => usePercentage());

      const firstResult = result.current;
      rerender();
      const secondResult = result.current;

      expect(firstResult).toBe(secondResult);
    });
  });

  describe('edge cases', () => {
    it('should handle missing translations gracefully', () => {
      // Re-mock translation to return key when missing
      vi.mocked(vi.fn()).mockImplementation(() => ({
        t: vi.fn((key: string) => key),
      }));

      expect(() => {
        renderHook(() => usePercentage());
      }).not.toThrow();
    });

    it('should generate decimal places from 1 to 5 (not 0)', () => {
      const { result } = renderHook(() => usePercentage());

      const options = result.current.decimalPlaces[0].options;
      const values = options.map((option) => option.value);

      expect(values).toEqual([1, 2, 3, 4, 5]);
      expect(values).not.toContain(0); // Different from useDecimalPlaces
    });

    it('should use Number.parseFloat consistently', () => {
      const { result } = renderHook(() => usePercentage());

      const options = result.current.decimalPlaces[0].options;

      // All selectedDisplay values should be based on Number.parseFloat('1')
      options.forEach((option) => {
        expect(option.selectedDisplay).toMatch(/^\d+ \(1\.\d+ %\)$/);
      });
    });
  });

  describe('component structure', () => {
    it('should return the hook result in expected format', () => {
      const { result } = renderHook(() => usePercentage());

      expect(result.current).toEqual({
        decimalPlaces: expect.arrayContaining([
          expect.objectContaining({
            options: expect.arrayContaining([
              expect.objectContaining({
                label: expect.any(Object),
                value: expect.any(Number),
                selectedDisplay: expect.any(String),
              }),
            ]),
          }),
        ]),
        displayOptions: expect.arrayContaining([
          expect.objectContaining({
            options: expect.arrayContaining([
              expect.objectContaining({
                value: expect.any(String),
                label: expect.any(Object),
              }),
            ]),
          }),
        ]),
      });
    });

    it('should not throw errors during hook execution', () => {
      expect(() => {
        renderHook(() => usePercentage());
      }).not.toThrow();
    });

    it('should have exactly 2 display options and 5 decimal place options', () => {
      const { result } = renderHook(() => usePercentage());

      expect(result.current.displayOptions[0].options).toHaveLength(2);
      expect(result.current.decimalPlaces[0].options).toHaveLength(5);
    });
  });
});
