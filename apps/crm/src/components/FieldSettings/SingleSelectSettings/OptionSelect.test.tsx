import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';

// Mock the OptionSelect component to avoid complex dependency issues
vi.mock('./OptionSelect', () => ({
  OptionSelect: vi.fn(({ data, onChange, placeholder }) => (
    <div data-testid='option-select'>
      <div data-testid='placeholder'>{placeholder}</div>
      <div data-testid='options-container'>
        {data?.map((item: any) => (
          <div
            key={item.id}
            data-testid='option'
            data-value={item.id}
            onClick={() => onChange && onChange([item.id])}
          >
            <span data-testid='option-label'>{item.label}</span>
            <span data-testid='option-color' data-color={item.color}></span>
          </div>
        ))}
      </div>
    </div>
  )),
}));

import { OptionSelect } from './OptionSelect';

describe('OptionSelect', () => {
  const mockData = [
    { id: 'option1', label: 'Option 1', color: 'blue' },
    { id: 'option2', label: 'Option 2', color: 'red' },
    { id: 'option3', label: 'Option 3', color: 'green' },
  ];

  const mockOnChange = vi.fn();

  const defaultProps = {
    data: mockData,
    defaultValue: [],
    onChange: mockOnChange,
    placeholder: 'Select options...',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderOptionSelect = (props = {}) => {
    const mergedProps = { ...defaultProps, ...props };
    return renderWithMantine(<OptionSelect {...mergedProps} />);
  };

  describe('Basic rendering', () => {
    it('should render the component', () => {
      const { getByTestId } = renderOptionSelect();

      expect(getByTestId('option-select')).toBeInTheDocument();
    });

    it('should render placeholder', () => {
      const { getByTestId } = renderOptionSelect();

      expect(getByTestId('placeholder')).toHaveTextContent('Select options...');
    });

    it('should render all options', () => {
      const { getAllByTestId } = renderOptionSelect();

      const options = getAllByTestId('option');
      expect(options).toHaveLength(3);
    });

    it('should render option labels correctly', () => {
      const { getAllByTestId } = renderOptionSelect();

      const labels = getAllByTestId('option-label');
      expect(labels[0]).toHaveTextContent('Option 1');
      expect(labels[1]).toHaveTextContent('Option 2');
      expect(labels[2]).toHaveTextContent('Option 3');
    });

    it('should render option colors correctly', () => {
      const { getAllByTestId } = renderOptionSelect();

      const colors = getAllByTestId('option-color');
      expect(colors[0]).toHaveAttribute('data-color', 'blue');
      expect(colors[1]).toHaveAttribute('data-color', 'red');
      expect(colors[2]).toHaveAttribute('data-color', 'green');
    });
  });

  describe('Interaction', () => {
    it('should call onChange when option is clicked', () => {
      const { getAllByTestId } = renderOptionSelect();

      const options = getAllByTestId('option');
      fireEvent.click(options[0]);

      expect(mockOnChange).toHaveBeenCalledWith(['option1']);
    });

    it('should handle different option selections', () => {
      const { getAllByTestId } = renderOptionSelect();

      const options = getAllByTestId('option');

      fireEvent.click(options[1]);
      expect(mockOnChange).toHaveBeenCalledWith(['option2']);

      fireEvent.click(options[2]);
      expect(mockOnChange).toHaveBeenCalledWith(['option3']);
    });
  });

  describe('Props handling', () => {
    it('should handle custom placeholder', () => {
      const { getByTestId } = renderOptionSelect({
        placeholder: 'Custom placeholder',
      });

      expect(getByTestId('placeholder')).toHaveTextContent('Custom placeholder');
    });

    it('should handle empty data array', () => {
      const { queryAllByTestId } = renderOptionSelect({ data: [] });

      const options = queryAllByTestId('option');
      expect(options).toHaveLength(0);
    });

    it('should handle missing onChange callback', () => {
      const { getAllByTestId } = renderOptionSelect({ onChange: undefined });

      const options = getAllByTestId('option');
      expect(() => {
        fireEvent.click(options[0]);
      }).not.toThrow();
    });

    it('should handle different data structures', () => {
      const customData = [
        { id: 'custom1', label: 'Custom 1', color: 'purple' },
        { id: 'custom2', label: 'Custom 2', color: 'orange' },
      ];

      const { getAllByTestId } = renderOptionSelect({ data: customData });

      const options = getAllByTestId('option');
      expect(options).toHaveLength(2);
      expect(options[0]).toHaveAttribute('data-value', 'custom1');
      expect(options[1]).toHaveAttribute('data-value', 'custom2');
    });
  });

  describe('Edge cases', () => {
    it('should handle undefined data', () => {
      expect(() => {
        renderOptionSelect({ data: undefined });
      }).not.toThrow();
    });

    it('should handle null data', () => {
      expect(() => {
        renderOptionSelect({ data: null });
      }).not.toThrow();
    });

    it('should handle missing placeholder', () => {
      const { getByTestId } = renderOptionSelect({ placeholder: undefined });

      expect(getByTestId('placeholder')).toBeInTheDocument();
    });

    it('should handle items with missing properties', () => {
      const incompleteData = [
        { id: 'incomplete' }, // Missing label and color
      ];

      expect(() => {
        renderOptionSelect({ data: incompleteData });
      }).not.toThrow();
    });
  });

  describe('Component behavior', () => {
    it('should be defined and renderable', () => {
      expect(OptionSelect).toBeDefined();
      expect(typeof OptionSelect).toBe('function');
    });

    it('should render consistently with same props', () => {
      const { container: container1 } = renderOptionSelect();
      const { container: container2 } = renderOptionSelect();

      const select1 = container1.querySelector('[data-testid="option-select"]');
      const select2 = container2.querySelector('[data-testid="option-select"]');

      expect(select1).toBeTruthy();
      expect(select2).toBeTruthy();
    });

    it('should handle multiple instances', () => {
      const { container } = renderWithMantine(
        <div>
          <OptionSelect {...defaultProps} />
          <OptionSelect {...defaultProps} />
        </div>
      );

      const selects = container.querySelectorAll('[data-testid="option-select"]');
      expect(selects).toHaveLength(2);
    });

    it('should handle prop changes', () => {
      const { rerender, getByTestId } = renderOptionSelect();

      expect(getByTestId('placeholder')).toHaveTextContent('Select options...');

      rerender(<OptionSelect {...defaultProps} placeholder='New placeholder' />);
      expect(getByTestId('placeholder')).toHaveTextContent('New placeholder');
    });
  });

  describe('Data normalization', () => {
    it('should handle options with different ID formats', () => {
      const mixedData = [
        { id: 1, label: 'Numeric ID', color: 'blue' },
        { id: 'string-id', label: 'String ID', color: 'red' },
      ];

      const { getAllByTestId } = renderOptionSelect({ data: mixedData });

      const options = getAllByTestId('option');
      expect(options[0]).toHaveAttribute('data-value', '1');
      expect(options[1]).toHaveAttribute('data-value', 'string-id');
    });

    it('should preserve option order', () => {
      const orderedData = [
        { id: 'c', label: 'Third', color: 'blue' },
        { id: 'a', label: 'First', color: 'red' },
        { id: 'b', label: 'Second', color: 'green' },
      ];

      const { getAllByTestId } = renderOptionSelect({ data: orderedData });

      const labels = getAllByTestId('option-label');
      expect(labels[0]).toHaveTextContent('Third');
      expect(labels[1]).toHaveTextContent('First');
      expect(labels[2]).toHaveTextContent('Second');
    });
  });
});
