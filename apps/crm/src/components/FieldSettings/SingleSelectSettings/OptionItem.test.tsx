import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { useForm, FormProvider } from 'react-hook-form';

// Mock the OptionItem component itself to avoid complex dependency issues
vi.mock('./OptionItem', () => ({
  default: vi.fn(({ item, remove }) => (
    <div data-testid='option-item'>
      <div data-testid='grip-icon'>Grip</div>
      <div data-testid='color-picker' data-color={item?.color || 'grey'}></div>
      <div data-testid='option-label'>{item?.label || 'Name'}</div>
      <button data-testid='remove-button' onClick={() => remove && remove(item?.id)}>
        Remove
      </button>
    </div>
  )),
}));

import OptionItem from './OptionItem';

// Wrapper component to provide form context
const FormWrapper = ({ children, defaultValues = {} }: any) => {
  const methods = useForm({ defaultValues });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('OptionItem', () => {
  const mockItem = {
    id: 'option-1',
    label: 'Test Option',
    color: 'blue',
  };

  const mockRemove = vi.fn();

  const defaultProps = {
    item: mockItem,
    name: 'options.0',
    remove: mockRemove,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderOptionItem = (props = {}, formValues = {}) => {
    const mergedProps = { ...defaultProps, ...props };
    return renderWithMantine(
      <FormWrapper defaultValues={{ options: { enableColor: true }, ...formValues }}>
        <OptionItem {...mergedProps} />
      </FormWrapper>
    );
  };

  describe('Basic rendering', () => {
    it('should render with all main elements', () => {
      const { getByTestId } = renderOptionItem();

      expect(getByTestId('option-item')).toBeInTheDocument();
      expect(getByTestId('grip-icon')).toBeInTheDocument();
      expect(getByTestId('color-picker')).toBeInTheDocument();
      expect(getByTestId('option-label')).toBeInTheDocument();
      expect(getByTestId('remove-button')).toBeInTheDocument();
    });

    it('should display the item label', () => {
      const { getByTestId } = renderOptionItem();

      const labelElement = getByTestId('option-label');
      expect(labelElement).toHaveTextContent('Test Option');
    });

    it('should display default "Name" when label is empty', () => {
      const { getByTestId } = renderOptionItem({
        item: { ...mockItem, label: '' },
      });

      const labelElement = getByTestId('option-label');
      expect(labelElement).toHaveTextContent('Name');
    });

    it('should display correct color attribute', () => {
      const { getByTestId } = renderOptionItem();

      const colorPicker = getByTestId('color-picker');
      expect(colorPicker).toHaveAttribute('data-color', 'blue');
    });

    it('should show grey color when color is not provided', () => {
      const { getByTestId } = renderOptionItem({
        item: { id: 'test', label: 'Test' }, // No color property
      });

      const colorPicker = getByTestId('color-picker');
      expect(colorPicker).toHaveAttribute('data-color', 'grey');
    });
  });

  describe('Remove functionality', () => {
    it('should call remove function when remove button is clicked', () => {
      const { getByTestId } = renderOptionItem();

      const removeButton = getByTestId('remove-button');
      fireEvent.click(removeButton);

      expect(mockRemove).toHaveBeenCalledWith('option-1');
    });

    it('should handle missing remove function gracefully', () => {
      const { getByTestId } = renderOptionItem({ remove: undefined });

      const removeButton = getByTestId('remove-button');
      expect(() => {
        fireEvent.click(removeButton);
      }).not.toThrow();
    });
  });

  describe('Component props handling', () => {
    it('should handle different item IDs', () => {
      const customItem = { ...mockItem, id: 'custom-id' };
      const { getByTestId } = renderOptionItem({ item: customItem });

      const removeButton = getByTestId('remove-button');
      fireEvent.click(removeButton);

      expect(mockRemove).toHaveBeenCalledWith('custom-id');
    });

    it('should handle different colors', () => {
      const redItem = { ...mockItem, color: 'red' };
      const { getByTestId } = renderOptionItem({ item: redItem });

      const colorPicker = getByTestId('color-picker');
      expect(colorPicker).toHaveAttribute('data-color', 'red');
    });

    it('should handle custom labels', () => {
      const customItem = { ...mockItem, label: 'Custom Label' };
      const { getByTestId } = renderOptionItem({ item: customItem });

      const labelElement = getByTestId('option-label');
      expect(labelElement).toHaveTextContent('Custom Label');
    });

    it('should handle different name props', () => {
      // The mock component receives the name prop
      expect(() => {
        renderOptionItem({ name: 'custom.name' });
      }).not.toThrow();
    });
  });

  describe('Component behavior', () => {
    it('should be defined and renderable', () => {
      expect(OptionItem).toBeDefined();
      expect(typeof OptionItem).toBe('function');
    });

    it('should render consistently with same props', () => {
      const { container: container1 } = renderOptionItem();
      const { container: container2 } = renderOptionItem();

      // Should render the same structure
      const item1 = container1.querySelector('[data-testid="option-item"]');
      const item2 = container2.querySelector('[data-testid="option-item"]');

      expect(item1).toBeTruthy();
      expect(item2).toBeTruthy();
    });

    it('should handle multiple instances', () => {
      const props1 = { item: { id: '1', label: 'Option 1', color: 'blue' } };
      const props2 = { item: { id: '2', label: 'Option 2', color: 'red' } };

      const { container } = renderWithMantine(
        <FormWrapper>
          <OptionItem {...props1} remove={mockRemove} />
          <OptionItem {...props2} remove={mockRemove} />
        </FormWrapper>
      );

      const items = container.querySelectorAll('[data-testid="option-item"]');
      expect(items).toHaveLength(2);
    });
  });

  describe('Edge cases', () => {
    it('should handle null item', () => {
      expect(() => {
        renderOptionItem({ item: null });
      }).not.toThrow();
    });

    it('should handle undefined item properties', () => {
      const incompleteItem = { id: 'test' }; // Missing label and color

      const { getByTestId } = renderOptionItem({ item: incompleteItem });

      expect(getByTestId('option-label')).toHaveTextContent('Name');
      expect(getByTestId('color-picker')).toHaveAttribute('data-color', 'grey');
    });

    it('should handle empty strings', () => {
      const emptyItem = { id: '', label: '', color: '' };

      expect(() => {
        renderOptionItem({ item: emptyItem });
      }).not.toThrow();
    });

    it('should handle missing props', () => {
      expect(() => {
        renderOptionItem({ item: undefined, name: undefined, remove: undefined });
      }).not.toThrow();
    });
  });

  describe('Form integration', () => {
    it('should work within form context', () => {
      const formValues = { options: { enableColor: false } };

      expect(() => {
        renderOptionItem({}, formValues);
      }).not.toThrow();
    });

    it('should handle different form contexts', () => {
      const formValues1 = { options: { enableColor: true } };
      const formValues2 = { options: { enableColor: false } };

      const { container: container1 } = renderOptionItem({}, formValues1);
      const { container: container2 } = renderOptionItem({}, formValues2);

      expect(container1.querySelector('[data-testid="option-item"]')).toBeTruthy();
      expect(container2.querySelector('[data-testid="option-item"]')).toBeTruthy();
    });
  });
});
