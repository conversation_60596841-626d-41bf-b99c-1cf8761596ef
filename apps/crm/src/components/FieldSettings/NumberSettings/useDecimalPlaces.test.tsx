import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { useDecimalPlaces } from './useDecimalPlaces';

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  Flex: ({ children, justify, w }: any) => (
    <div data-testid='flex' data-justify={justify} data-w={w}>
      {children}
    </div>
  ),
  Text: ({ children, c }: any) => (
    <span data-testid='text' data-color={c}>
      {children}
    </span>
  ),
}));

describe('useDecimalPlaces', () => {
  it('should return decimal places options in correct structure', () => {
    const { result } = renderHook(() => useDecimalPlaces());

    expect(result.current).toHaveProperty('decimalPlaces');
    expect(Array.isArray(result.current.decimalPlaces)).toBe(true);
    expect(result.current.decimalPlaces).toHaveLength(1);
    expect(result.current.decimalPlaces[0]).toHaveProperty('options');
  });

  it('should generate correct number of decimal place options', () => {
    const { result } = renderHook(() => useDecimalPlaces());

    const options = result.current.decimalPlaces[0].options;

    // Should generate options for 0, 1, 2, 3, 4, 5 decimal places (6 total)
    expect(options).toHaveLength(6);
  });

  it('should generate correct option values and labels for each decimal place', () => {
    const { result } = renderHook(() => useDecimalPlaces());

    const options = result.current.decimalPlaces[0].options;

    // Test first option (0 decimal places)
    expect(options[0].value).toBe(0);
    expect(options[0].selectedDisplay).toBe('0 (1)');

    // Test middle option (2 decimal places)
    expect(options[2].value).toBe(2);
    expect(options[2].selectedDisplay).toBe('2 (1.00)');

    // Test last option (5 decimal places)
    expect(options[5].value).toBe(5);
    expect(options[5].selectedDisplay).toBe('5 (1.00000)');
  });

  it('should format decimal numbers correctly using toFixed', () => {
    const { result } = renderHook(() => useDecimalPlaces());

    const options = result.current.decimalPlaces[0].options;

    // Verify that toFixed is being used properly
    expect(options[0].selectedDisplay).toContain('1'); // 0 decimal places
    expect(options[1].selectedDisplay).toContain('1.0'); // 1 decimal place
    expect(options[3].selectedDisplay).toContain('1.000'); // 3 decimal places
  });

  it('should render Flex components for option labels', () => {
    const { result } = renderHook(() => useDecimalPlaces());

    const options = result.current.decimalPlaces[0].options;
    const firstOption = options[0];

    // Check that the label is a React element
    expect(firstOption.label).toBeDefined();
    expect(typeof firstOption.label).toBe('object');
  });

  it('should have selectedDisplay with number and formatted value', () => {
    const { result } = renderHook(() => useDecimalPlaces());

    const options = result.current.decimalPlaces[0].options;

    options.forEach((option, index) => {
      expect(option.selectedDisplay).toMatch(new RegExp(`^${index} \\(1`));
      expect(option.selectedDisplay).toContain(')');
    });
  });

  describe('memoization', () => {
    it('should maintain stable references across re-renders', () => {
      const { result, rerender } = renderHook(() => useDecimalPlaces());

      const firstRender = result.current.decimalPlaces;
      rerender();
      const secondRender = result.current.decimalPlaces;

      expect(firstRender).toBe(secondRender);
    });

    it('should maintain stable option references', () => {
      const { result, rerender } = renderHook(() => useDecimalPlaces());

      const firstOptions = result.current.decimalPlaces[0].options;
      rerender();
      const secondOptions = result.current.decimalPlaces[0].options;

      expect(firstOptions).toBe(secondOptions);
    });
  });

  describe('edge cases', () => {
    it('should handle boundary values correctly', () => {
      const { result } = renderHook(() => useDecimalPlaces());

      const options = result.current.decimalPlaces[0].options;

      // Test minimum (0 decimal places)
      const minOption = options[0];
      expect(minOption.value).toBe(0);
      expect(minOption.selectedDisplay).not.toContain('.');

      // Test maximum (5 decimal places)
      const maxOption = options[5];
      expect(maxOption.value).toBe(5);
      expect(maxOption.selectedDisplay).toContain('.00000');
    });

    it('should use Number.parseFloat correctly', () => {
      const { result } = renderHook(() => useDecimalPlaces());

      const options = result.current.decimalPlaces[0].options;

      // All selectedDisplay values should be based on Number.parseFloat('1')
      options.forEach((option) => {
        expect(option.selectedDisplay).toMatch(/^\d+ \(1(\.\d+)?\)$/);
      });
    });

    it('should generate sequential values from 0 to 5', () => {
      const { result } = renderHook(() => useDecimalPlaces());

      const options = result.current.decimalPlaces[0].options;
      const values = options.map((option) => option.value);

      expect(values).toEqual([0, 1, 2, 3, 4, 5]);
    });
  });

  describe('component structure', () => {
    it('should return the hook result in expected format', () => {
      const { result } = renderHook(() => useDecimalPlaces());

      // Verify the structure matches what's expected by consuming components
      expect(result.current).toEqual({
        decimalPlaces: expect.arrayContaining([
          expect.objectContaining({
            options: expect.arrayContaining([
              expect.objectContaining({
                label: expect.any(Object),
                value: expect.any(Number),
                selectedDisplay: expect.any(String),
              }),
            ]),
          }),
        ]),
      });
    });

    it('should not throw errors during hook execution', () => {
      expect(() => {
        renderHook(() => useDecimalPlaces());
      }).not.toThrow();
    });
  });
});
