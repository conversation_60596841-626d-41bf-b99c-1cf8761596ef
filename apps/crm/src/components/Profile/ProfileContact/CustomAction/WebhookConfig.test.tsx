import { fireEvent, screen } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { renderWithMantine } from '@/tests/utils/testUtils';
import WebhookConfig from './WebhookConfig';

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => `translated_${key}`,
  }),
}));

vi.mock('react-hook-form-mantine', () => ({
  Select: vi.fn(
    ({
      control,
      name,
      label,
      placeholder,
      data,
      rightSection,
      withAsterisk,
      allowDeselect,
      ...props
    }) => (
      <div data-testid={`select-${name?.split('.').pop()}`}>
        <label data-testid={`select-label-${name?.split('.').pop()}`}>
          {label}
          {withAsterisk && <span data-testid='asterisk'>*</span>}
        </label>
        <select
          data-testid={`select-input-${name?.split('.').pop()}`}
          placeholder={placeholder}
          {...props}
        >
          {data?.map((item: string) => (
            <option key={item} value={item}>
              {item}
            </option>
          ))}
        </select>
        {rightSection && <div data-testid='right-section'>{rightSection}</div>}
      </div>
    )
  ),
  Switch: vi.fn(({ control, name, label, color, ...props }) => (
    <div data-testid={`switch-${name?.split('.').pop()}`}>
      <label data-testid={`switch-label-${name?.split('.').pop()}`}>{label}</label>
      <input
        type='checkbox'
        data-testid={`switch-input-${name?.split('.').pop()}`}
        data-color={color}
        {...props}
      />
    </div>
  )),
  TextInput: vi.fn(
    ({ control, name, label, placeholder, w, withAsterisk, labelProps, ...props }) => (
      <div data-testid={`text-input-${name?.split('.').pop()}`} style={{ width: w }}>
        <label data-testid={`text-input-label-${name?.split('.').pop()}`}>
          {label}
          {withAsterisk && <span data-testid='asterisk'>*</span>}
        </label>
        <input
          data-testid={`text-input-field-${name?.split('.').pop()}`}
          placeholder={placeholder}
          {...props}
        />
      </div>
    )
  ),
}));

vi.mock('./ColorConfig', () => ({
  default: vi.fn(({ name }) => (
    <div data-testid='color-config' data-name={name}>
      Color Config Component
    </div>
  )),
}));

vi.mock('./IconConfig', () => ({
  default: vi.fn(({ name }) => (
    <div data-testid='icon-config' data-name={name}>
      Icon Config Component
    </div>
  )),
}));

vi.mock('./Parameters', () => ({
  default: vi.fn(({ prefixName }) => (
    <div data-testid='parameters' data-prefix-name={prefixName}>
      Parameters Component
    </div>
  )),
}));

vi.mock('@tabler/icons-react', () => ({
  IconChevronDown: vi.fn(({ size }) => (
    <div data-testid='icon-chevron-down' data-size={size}>
      ChevronDown
    </div>
  )),
}));

// Test wrapper component with form provider
const TestWrapper = ({
  children,
  defaultValues = {},
}: {
  children: React.ReactNode;
  defaultValues?: any;
}) => {
  const methods = useForm({
    defaultValues: {
      webhook: {
        method: '',
        url: '',
        sendHeaders: {
          enabled: false,
        },
        sendBody: {
          enabled: false,
        },
        color: 'red',
        icon: 'phone',
      },
      ...defaultValues,
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('WebhookConfig Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render method selection dropdown', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-method')).toBeInTheDocument();
      expect(screen.getByTestId('select-label-method')).toHaveTextContent('translated_method');
      expect(screen.getByTestId('select-input-method')).toHaveAttribute(
        'placeholder',
        'translated_selectMethod'
      );

      // Check that asterisks are present (there are multiple)
      const asterisks = screen.getAllByTestId('asterisk');
      expect(asterisks.length).toBeGreaterThan(0); // withAsterisk
    });

    it('should render URL input field', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getByTestId('text-input-url')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-label-url')).toHaveTextContent('URL');
      expect(screen.getByTestId('text-input-field-url')).toHaveAttribute(
        'placeholder',
        'translated_inputUrl'
      );
    });

    it('should render send headers switch', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      // Get all switches since there are multiple with the same test ID
      const switches = screen.getAllByTestId('switch-enabled');
      expect(switches[0]).toBeInTheDocument();

      const switchLabels = screen.getAllByTestId('switch-label-enabled');
      expect(switchLabels[0]).toHaveTextContent('translated_sendHeaders (translated_optional)');

      const switchInputs = screen.getAllByTestId('switch-input-enabled');
      expect(switchInputs[0]).toHaveAttribute('data-color', 'decaGreen');
    });

    it('should render send body switch', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      const bodySwitches = screen.getAllByTestId('switch-enabled');
      expect(bodySwitches).toHaveLength(2); // Headers and body switches

      const sendBodyLabel = screen.getByText('translated_sendBody (translated_optional)');
      expect(sendBodyLabel).toBeInTheDocument();
    });

    it('should render color config component', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getByTestId('color-config')).toBeInTheDocument();
      expect(screen.getByTestId('color-config')).toHaveAttribute('data-name', 'webhook.color');
    });

    it('should render icon config component', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getByTestId('icon-config')).toBeInTheDocument();
      expect(screen.getByTestId('icon-config')).toHaveAttribute('data-name', 'webhook.icon');
    });

    it('should render dropdown with chevron icon', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      // Find the chevron icon by its svg class instead of test ID
      const chevronIcon = document.querySelector('.tabler-icon-chevron-down');
      expect(chevronIcon).toBeInTheDocument();
      expect(chevronIcon).toHaveAttribute('height', '16');
    });
  });

  describe('Method Selection', () => {
    it('should render all HTTP method options', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      const methods = ['Get', 'Post', 'Patch', 'Put', 'Delete'];
      methods.forEach((method) => {
        expect(screen.getByText(method)).toBeInTheDocument();
      });
    });

    it('should have allowDeselect set to false', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      // This is tested through the mock props
      expect(screen.getByTestId('select-method')).toBeInTheDocument();
    });
  });

  describe('Conditional Parameters Rendering', () => {
    it('should render parameters component when sendHeaders is enabled', () => {
      renderWithMantine(
        <TestWrapper
          defaultValues={{
            webhook: {
              sendHeaders: { enabled: true },
            },
          }}
        >
          <WebhookConfig />
        </TestWrapper>
      );

      const parametersComponents = screen.getAllByTestId('parameters');
      const headersParameters = parametersComponents.find(
        (comp) => comp.getAttribute('data-prefix-name') === 'webhook.sendHeaders'
      );
      expect(headersParameters).toBeInTheDocument();
    });

    it('should render parameters component when sendBody is enabled', () => {
      renderWithMantine(
        <TestWrapper
          defaultValues={{
            webhook: {
              sendBody: { enabled: true },
            },
          }}
        >
          <WebhookConfig />
        </TestWrapper>
      );

      const parametersComponents = screen.getAllByTestId('parameters');
      const bodyParameters = parametersComponents.find(
        (comp) => comp.getAttribute('data-prefix-name') === 'webhook.sendBody'
      );
      expect(bodyParameters).toBeInTheDocument();
    });

    it('should not render parameters when switches are disabled', () => {
      renderWithMantine(
        <TestWrapper
          defaultValues={{
            webhook: {
              sendHeaders: { enabled: false },
              sendBody: { enabled: false },
            },
          }}
        >
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.queryByTestId('parameters')).not.toBeInTheDocument();
    });

    it('should render both parameters when both switches are enabled', () => {
      renderWithMantine(
        <TestWrapper
          defaultValues={{
            webhook: {
              sendHeaders: { enabled: true },
              sendBody: { enabled: true },
            },
          }}
        >
          <WebhookConfig />
        </TestWrapper>
      );

      const parametersComponents = screen.getAllByTestId('parameters');
      expect(parametersComponents).toHaveLength(2);

      const headersParameters = parametersComponents.find(
        (comp) => comp.getAttribute('data-prefix-name') === 'webhook.sendHeaders'
      );
      const bodyParameters = parametersComponents.find(
        (comp) => comp.getAttribute('data-prefix-name') === 'webhook.sendBody'
      );

      expect(headersParameters).toBeInTheDocument();
      expect(bodyParameters).toBeInTheDocument();
    });
  });

  describe('Form Integration', () => {
    it('should integrate with react-hook-form context', () => {
      const TestComponent = () => {
        const methods = useForm({
          defaultValues: {
            webhook: {
              method: 'POST',
              url: 'https://api.example.com',
              sendHeaders: { enabled: true },
              sendBody: { enabled: false },
              color: 'blue',
              icon: 'email',
            },
          },
        });

        return (
          <FormProvider {...methods}>
            <WebhookConfig />
            <div data-testid='form-state'>{JSON.stringify(methods.watch('webhook'))}</div>
          </FormProvider>
        );
      };

      renderWithMantine(<TestComponent />);

      const formState = screen.getByTestId('form-state');
      expect(formState).toHaveTextContent('POST');
      expect(formState).toHaveTextContent('https://api.example.com');
      expect(formState).toHaveTextContent('blue');
      expect(formState).toHaveTextContent('email');
    });

    it('should update form state when switches are toggled', () => {
      const TestComponent = () => {
        const methods = useForm({
          defaultValues: {
            webhook: {
              sendHeaders: { enabled: false },
              sendBody: { enabled: false },
            },
          },
        });

        return (
          <FormProvider {...methods}>
            <WebhookConfig />
            <button
              type='button'
              onClick={() => methods.setValue('webhook.sendHeaders.enabled', true)}
              data-testid='enable-headers'
            >
              Enable Headers
            </button>
            <button
              type='button'
              onClick={() => methods.setValue('webhook.sendBody.enabled', true)}
              data-testid='enable-body'
            >
              Enable Body
            </button>
            <div data-testid='headers-enabled'>
              {String(methods.watch('webhook.sendHeaders.enabled'))}
            </div>
            <div data-testid='body-enabled'>
              {String(methods.watch('webhook.sendBody.enabled'))}
            </div>
          </FormProvider>
        );
      };

      renderWithMantine(<TestComponent />);

      const headersEnabled = screen.getByTestId('headers-enabled');
      const bodyEnabled = screen.getByTestId('body-enabled');

      expect(headersEnabled).toHaveTextContent('false');
      expect(bodyEnabled).toHaveTextContent('false');

      fireEvent.click(screen.getByTestId('enable-headers'));
      expect(headersEnabled).toHaveTextContent('true');

      fireEvent.click(screen.getByTestId('enable-body'));
      expect(bodyEnabled).toHaveTextContent('true');
    });
  });

  describe('Layout and Styling', () => {
    it('should apply correct flex layout for method and URL', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      // Both method select and URL input should be present
      expect(screen.getByTestId('select-method')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-url')).toBeInTheDocument();
    });

    it('should apply full width to URL input', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      const urlInput = screen.getByTestId('text-input-url');
      expect(urlInput).toHaveStyle({ width: '100%' });
    });

    it('should render components in correct order', () => {
      renderWithMantine(
        <TestWrapper
          defaultValues={{
            webhook: {
              sendHeaders: { enabled: true },
              sendBody: { enabled: true },
            },
          }}
        >
          <WebhookConfig />
        </TestWrapper>
      );

      const elements = [
        screen.getByTestId('select-method'),
        screen.getByTestId('text-input-url'),
        screen.getAllByTestId('switch-enabled')[0], // Headers switch
        screen.getAllByTestId('parameters')[0], // Headers parameters
        screen.getAllByTestId('switch-enabled')[1], // Body switch
        screen.getAllByTestId('parameters')[1], // Body parameters
        screen.getByTestId('color-config'),
        screen.getByTestId('icon-config'),
      ];

      // Verify all elements are present
      elements.forEach((element) => {
        expect(element).toBeInTheDocument();
      });
    });
  });

  describe('Component Props and Integration', () => {
    it('should pass correct props to child components', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      // ColorConfig should receive correct name prop
      expect(screen.getByTestId('color-config')).toHaveAttribute('data-name', 'webhook.color');

      // IconConfig should receive correct name prop
      expect(screen.getByTestId('icon-config')).toHaveAttribute('data-name', 'webhook.icon');
    });

    it('should pass correct prefixName to Parameters components', () => {
      renderWithMantine(
        <TestWrapper
          defaultValues={{
            webhook: {
              sendHeaders: { enabled: true },
              sendBody: { enabled: true },
            },
          }}
        >
          <WebhookConfig />
        </TestWrapper>
      );

      const parametersComponents = screen.getAllByTestId('parameters');

      const headersParameters = parametersComponents.find(
        (comp) => comp.getAttribute('data-prefix-name') === 'webhook.sendHeaders'
      );
      const bodyParameters = parametersComponents.find(
        (comp) => comp.getAttribute('data-prefix-name') === 'webhook.sendBody'
      );

      expect(headersParameters).toBeInTheDocument();
      expect(bodyParameters).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined webhook values gracefully', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{ webhook: undefined }}>
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-method')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-url')).toBeInTheDocument();
    });

    it('should handle null sendHeaders/sendBody values', () => {
      renderWithMantine(
        <TestWrapper
          defaultValues={{
            webhook: {
              sendHeaders: null,
              sendBody: null,
            },
          }}
        >
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getAllByTestId('switch-enabled')).toHaveLength(2);
      expect(screen.queryByTestId('parameters')).not.toBeInTheDocument();
    });

    it('should handle missing enabled property in switches', () => {
      renderWithMantine(
        <TestWrapper
          defaultValues={{
            webhook: {
              sendHeaders: {},
              sendBody: {},
            },
          }}
        >
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getAllByTestId('switch-enabled')).toHaveLength(2);
      expect(screen.queryByTestId('parameters')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper labels for all form elements', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-label-method')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-label-url')).toBeInTheDocument();
      expect(screen.getAllByTestId('switch-label-enabled')).toHaveLength(2);
    });

    it('should indicate required fields with asterisk', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      const asterisks = screen.getAllByTestId('asterisk');
      expect(asterisks.length).toBeGreaterThanOrEqual(2); // Method and URL are required
    });

    it('should have proper switch colors for accessibility', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      const switches = screen.getAllByTestId('switch-input-enabled');
      switches.forEach((switchInput) => {
        expect(switchInput).toHaveAttribute('data-color', 'decaGreen');
      });
    });
  });

  describe('Translation Integration', () => {
    it('should use correct translation keys', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getByText('translated_method')).toBeInTheDocument();
      expect(screen.getByText('URL')).toBeInTheDocument(); // URL is not translated
      // Check placeholder attribute instead of text content
      expect(screen.getByPlaceholderText('translated_selectMethod')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('translated_inputUrl')).toBeInTheDocument();
      expect(screen.getByText('translated_sendHeaders (translated_optional)')).toBeInTheDocument();
      expect(screen.getByText('translated_sendBody (translated_optional)')).toBeInTheDocument();
    });

    it('should format optional labels correctly', () => {
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      // Both switches should have (optional) in their labels
      expect(screen.getByText('translated_sendHeaders (translated_optional)')).toBeInTheDocument();
      expect(screen.getByText('translated_sendBody (translated_optional)')).toBeInTheDocument();
    });
  });

  describe('Component Memoization', () => {
    it('should be memoized with React.memo', () => {
      // Simple test to verify the component is memoized
      // Test that it renders without errors when properly wrapped
      renderWithMantine(
        <TestWrapper>
          <WebhookConfig />
        </TestWrapper>
      );

      expect(screen.getByText('translated_method')).toBeInTheDocument();
    });
  });
});
