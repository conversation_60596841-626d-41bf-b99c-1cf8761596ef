import { renderWithMantine } from '@/tests/utils/testUtils';
import { describe, expect, it, vi } from 'vitest';
import dayjs from 'dayjs';
import { getIdentityBadge, groupDataByDate } from './helpers';
import type { IdentityType } from '@/models/identity';
import type { TFnType } from '@tolgee/react';

// Mock DecaStatus component
vi.mock('@resola-ai/ui', () => ({
  DecaStatus: vi.fn(({ variant, tt, children }) => (
    <div data-testid='deca-status' data-variant={variant} data-tt={tt}>
      {children}
    </div>
  )),
}));

describe('helpers', () => {
  describe('getIdentityBadge', () => {
    const mockT: TFnType = vi.fn((props: any) => {
      const key = typeof props === 'string' ? props : props.key;
      const translations: Record<string, string> = {
        'identity.email': 'Email',
        'identity.lineId': 'Line ID',
        'identity.phone': 'Phone',
        'identity.webuserId': 'Web User ID',
      };
      return translations[key] || key;
    });

    it('should return yellow badge for email type', () => {
      const { getByTestId } = renderWithMantine(
        <div>{getIdentityBadge('email' as IdentityType, mockT)}</div>
      );

      const badge = getByTestId('deca-status');
      expect(badge).toHaveAttribute('data-variant', 'yellow');
      expect(badge).toHaveTextContent('Email');
      expect(mockT).toHaveBeenCalledWith('identity.email');
    });

    it('should return green uppercase badge for lineId type', () => {
      const { getByTestId } = renderWithMantine(
        <div>{getIdentityBadge('lineId' as IdentityType, mockT)}</div>
      );

      const badge = getByTestId('deca-status');
      expect(badge).toHaveAttribute('data-variant', 'green');
      expect(badge).toHaveAttribute('data-tt', 'uppercase');
      expect(badge).toHaveTextContent('Line ID');
      expect(mockT).toHaveBeenCalledWith('identity.lineId');
    });

    it('should return blue badge for phone type', () => {
      const { getByTestId } = renderWithMantine(
        <div>{getIdentityBadge('phone' as IdentityType, mockT)}</div>
      );

      const badge = getByTestId('deca-status');
      expect(badge).toHaveAttribute('data-variant', 'blue');
      expect(badge).toHaveTextContent('Phone');
      expect(mockT).toHaveBeenCalledWith('identity.phone');
    });

    it('should return violet badge for webuserId type', () => {
      const { getByTestId } = renderWithMantine(
        <div>{getIdentityBadge('webuserId' as IdentityType, mockT)}</div>
      );

      const badge = getByTestId('deca-status');
      expect(badge).toHaveAttribute('data-variant', 'violet');
      expect(badge).toHaveTextContent('Web User ID');
      expect(mockT).toHaveBeenCalledWith('identity.webuserId');
    });

    it('should return null for unknown type', () => {
      const result = getIdentityBadge('unknown' as IdentityType, mockT);
      expect(result).toBeNull();
    });

    it('should handle all identity types correctly', () => {
      const types: IdentityType[] = ['email', 'lineId', 'phone', 'webuserId'];
      const expectedVariants = ['yellow', 'green', 'blue', 'violet'];

      types.forEach((type, index) => {
        const { getByTestId, unmount } = renderWithMantine(
          <div>{getIdentityBadge(type, mockT)}</div>
        );

        const badge = getByTestId('deca-status');
        expect(badge).toHaveAttribute('data-variant', expectedVariants[index]);
        unmount();
      });
    });
  });

  describe('groupDataByDate', () => {
    const now = dayjs();
    const weekStart = now.startOf('week');
    const weekEnd = now.endOf('week');

    it('should group data into thisWeek for dates within current week', () => {
      const testData = [
        { id: 1, updatedAt: weekStart.format('YYYY-MM-DD') },
        { id: 2, updatedAt: weekStart.add(1, 'day').format('YYYY-MM-DD') },
        { id: 3, updatedAt: weekEnd.format('YYYY-MM-DD') },
      ];

      const result = groupDataByDate(testData, 'updatedAt');

      expect(result.get('thisWeek')).toHaveLength(3);
      expect(result.get('thisWeek')).toEqual(testData);
    });

    it('should group data by specific dates for dates outside current week', () => {
      const lastWeek = weekStart.subtract(1, 'week');
      const nextWeek = weekEnd.add(1, 'week');

      const testData = [
        { id: 1, updatedAt: lastWeek.format('YYYY-MM-DD') },
        { id: 2, updatedAt: nextWeek.format('YYYY-MM-DD') },
      ];

      const result = groupDataByDate(testData, 'updatedAt');

      expect(result.get('thisWeek')).toHaveLength(0);
      expect(result.get(lastWeek.format('DD/MM/YYYY'))).toHaveLength(1);
      expect(result.get(nextWeek.format('DD/MM/YYYY'))).toHaveLength(1);
    });

    it('should handle mixed data with both thisWeek and other dates', () => {
      const today = now;
      const lastMonth = now.subtract(1, 'month');

      const testData = [
        { id: 1, updatedAt: today.format('YYYY-MM-DD') },
        { id: 2, updatedAt: lastMonth.format('YYYY-MM-DD') },
        { id: 3, updatedAt: weekStart.format('YYYY-MM-DD') },
      ];

      const result = groupDataByDate(testData, 'updatedAt');

      expect(result.get('thisWeek')).toHaveLength(2);
      expect(result.get(lastMonth.format('DD/MM/YYYY'))).toHaveLength(1);
    });

    it('should handle empty data array', () => {
      const result = groupDataByDate([], 'updatedAt');

      expect(result.get('thisWeek')).toHaveLength(0);
      expect(result.size).toBe(1); // Only thisWeek key exists
    });

    it('should handle data with different field names', () => {
      const testData = [
        { id: 1, createdAt: weekStart.format('YYYY-MM-DD') },
        { id: 2, createdAt: weekStart.subtract(1, 'week').format('YYYY-MM-DD') },
      ];

      const result = groupDataByDate(testData, 'createdAt');

      expect(result.get('thisWeek')).toHaveLength(1);
      expect(result.get('thisWeek')?.[0]).toEqual(testData[0]);
    });

    it('should handle multiple items on the same date', () => {
      const sameDate = weekStart.subtract(1, 'week');
      const testData = [
        { id: 1, updatedAt: sameDate.format('YYYY-MM-DD') },
        { id: 2, updatedAt: sameDate.format('YYYY-MM-DD') },
        { id: 3, updatedAt: sameDate.format('YYYY-MM-DD') },
      ];

      const result = groupDataByDate(testData, 'updatedAt');

      const dateKey = sameDate.format('DD/MM/YYYY');
      expect(result.get(dateKey)).toHaveLength(3);
      expect(result.get(dateKey)).toEqual(testData);
    });

    it('should handle edge cases at week boundaries', () => {
      // Test the exact start and end of week
      const testData = [
        { id: 1, updatedAt: weekStart.subtract(1, 'millisecond').format('YYYY-MM-DD') },
        { id: 2, updatedAt: weekStart.format('YYYY-MM-DD') },
        { id: 3, updatedAt: weekEnd.format('YYYY-MM-DD') },
        { id: 4, updatedAt: weekEnd.add(1, 'millisecond').format('YYYY-MM-DD') },
      ];

      const result = groupDataByDate(testData, 'updatedAt');

      expect(result.get('thisWeek')).toHaveLength(2);
      expect(result.get('thisWeek')).toEqual([testData[1], testData[2]]);
    });

    it('should preserve data integrity during grouping', () => {
      const complexData = [
        {
          id: 1,
          name: 'Item 1',
          value: 100,
          updatedAt: weekStart.format('YYYY-MM-DD'),
          metadata: { key: 'value' },
        },
        {
          id: 2,
          name: 'Item 2',
          value: 200,
          updatedAt: weekStart.subtract(1, 'week').format('YYYY-MM-DD'),
          metadata: { key: 'value2' },
        },
      ];

      const result = groupDataByDate(complexData, 'updatedAt');

      expect(result.get('thisWeek')?.[0]).toEqual(complexData[0]);
      const lastWeekKey = weekStart.subtract(1, 'week').format('DD/MM/YYYY');
      expect(result.get(lastWeekKey)?.[0]).toEqual(complexData[1]);
    });

    it('should maintain chronological order within groups', () => {
      const testData = [
        { id: 1, updatedAt: weekStart.add(2, 'day').format('YYYY-MM-DD') },
        { id: 2, updatedAt: weekStart.format('YYYY-MM-DD') },
        { id: 3, updatedAt: weekStart.add(1, 'day').format('YYYY-MM-DD') },
      ];

      const result = groupDataByDate(testData, 'updatedAt');

      expect(result.get('thisWeek')).toEqual(testData); // Should preserve original order
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle undefined translation function by throwing error', () => {
      expect(() => getIdentityBadge('email' as IdentityType, undefined as any)).toThrow();
    });

    it('should handle invalid date strings in groupDataByDate', () => {
      const testData = [
        { id: 1, updatedAt: 'invalid-date' },
        { id: 2, updatedAt: '' },
        { id: 3, updatedAt: null },
      ];

      expect(() => groupDataByDate(testData, 'updatedAt')).not.toThrow();
    });

    it('should handle objects without the specified field', () => {
      const testData = [
        { id: 1 }, // Missing updatedAt field
        { id: 2, updatedAt: undefined },
      ];

      expect(() => groupDataByDate(testData, 'updatedAt')).not.toThrow();
    });
  });
});
