import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import ChildObjectFields from './ChildObjectFields';

// Setup test environment
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock all dependencies
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(() => ({ wsId: 'workspace-1' })),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(() => ({
    object: { id: 'parent-obj', childObjects: [] },
    refetchObject: vi.fn(),
  })),
}));

vi.mock('@/services/api', () => ({
  ObjectAPI: { update: vi.fn().mockResolvedValue({}) },
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: vi.fn((key, options) => {
      if (key === 'totalFields' && options) {
        return `${options.num} of ${options.total} fields`;
      }
      return key;
    }),
  })),
}));

vi.mock('@/constants/workspace', () => ({
  ColumnIcon: { 'single-line-text': 'text-icon' },
}));

vi.mock('@resola-ai/ui/components', () => ({
  FieldTypes: { SINGLE_LINE_TEXT: 'single-line-text' },
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn(() => ({ classes: { accordion: 'accordion' } })),
}));

vi.mock('../ObjectFields/FieldRender', () => ({
  default: ({ fields }: any) => (
    <div data-testid='field-render'>
      <div data-testid='field-count'>{fields.length}</div>
    </div>
  ),
}));

vi.mock('@tabler/icons-react', () => ({
  IconPinnedOff: () => <div data-testid='icon-pinned-off'>Unpin</div>,
}));

describe('ChildObjectFields', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockProps = {
    form: {
      linkedObj: {
        id: 'child-obj-1',
        name: { singular: 'Contact', plural: 'Contacts' },
        fields: [
          { id: 'name', name: 'Name', type: 'single-line-text', options: {} },
          { id: 'email', name: 'Email', type: 'email', options: {} },
        ],
      } as any,
      records: [
        { id: 'record-1', name: 'John Doe', email: '<EMAIL>' },
        { id: 'record-2', name: 'Jane Smith', email: '<EMAIL>' },
      ],
      template: 'default',
    },
  };

  describe('Basic Rendering', () => {
    it('should render the component with records', () => {
      const { getByTestId } = renderWithMantine(<ChildObjectFields {...mockProps} />);
      expect(getByTestId('child-object-fields-test-id')).toBeInTheDocument();
    });

    it('should display the object name', () => {
      const { getByText } = renderWithMantine(<ChildObjectFields {...mockProps} />);
      expect(getByText('Contact')).toBeInTheDocument();
    });

    it('should render unpin button', () => {
      const { getByText } = renderWithMantine(<ChildObjectFields {...mockProps} />);
      expect(getByText('unpinTabbar')).toBeInTheDocument();
    });

    it('should render accordion for each record', () => {
      const { getByText } = renderWithMantine(<ChildObjectFields {...mockProps} />);
      expect(getByText('Contact - record-1')).toBeInTheDocument();
      expect(getByText('Contact - record-2')).toBeInTheDocument();
    });
  });

  describe('FieldRender Integration', () => {
    it('should pass correct field count to FieldRender', () => {
      const { getAllByTestId } = renderWithMantine(<ChildObjectFields {...mockProps} />);
      const fieldCounts = getAllByTestId('field-count');
      expect(fieldCounts[0]).toHaveTextContent('2');
    });
  });

  describe('Empty States', () => {
    it('should render empty state when no linkedObj provided', () => {
      const emptyProps = { form: {} as any };
      const { getByText } = renderWithMantine(<ChildObjectFields {...emptyProps} />);
      expect(getByText('noDataAvailable')).toBeInTheDocument();
    });

    it('should render empty state when no records provided', () => {
      const emptyRecordsProps = {
        form: { linkedObj: mockProps.form.linkedObj, records: [] },
      };
      const { getByText } = renderWithMantine(<ChildObjectFields {...emptyRecordsProps} />);
      expect(getByText('noDataAvailable')).toBeInTheDocument();
    });
  });

  describe('Unpin Modal', () => {
    it('should render unpin button', () => {
      const { getByText } = renderWithMantine(<ChildObjectFields {...mockProps} />);

      const unpinButton = getByText('unpinTabbar');
      expect(unpinButton).toBeInTheDocument();

      // Button should be clickable
      fireEvent.click(unpinButton);
      // Just verify the button exists and is clickable, modal behavior may depend on external state
    });
  });
});
