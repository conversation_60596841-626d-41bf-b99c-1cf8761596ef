import { Flex, rem, Text } from '@mantine/core';
import { CustomImage, DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

interface FailedToUploadProps {
  onReset?: () => void;
  imageUrl: string;
  title: string;
  description: string;
}

const FailedToUpload: React.FC<FailedToUploadProps> = ({
  onReset,
  imageUrl,
  title,
  description,
}) => {
  const { t } = useTranslate('workspace');

  return (
    <Flex
      direction='column'
      align='center'
      justify='center'
      h={'100%'}
      data-testid='error-container'
      mt={rem(20)}
    >
      <CustomImage url={imageUrl} data-testid='csv-import-error-image' />
      <Text mt={rem(24)} fz={rem(14)} ta='center' c='decaGrey.7' data-testid='max-file-size-error'>
        {title}
      </Text>
      <Text mt={rem(20)} mb={rem(12)} fz={rem(14)} data-testid='upload-failed-error' ta='center'>
        {description}
      </Text>
      <DecaButton
        variant='negative_text'
        size='sm'
        onClick={onReset}
        data-testid='csv-file-info-reset-button'
      >
        {t('resetUpload')}
      </DecaButton>
    </Flex>
  );
};

export default FailedToUpload;
