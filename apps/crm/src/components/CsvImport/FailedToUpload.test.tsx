import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import FailedToUpload from './FailedToUpload';

// Mock translations
const translations: Record<string, string> = {
  resetUpload: 'Reset Upload',
};

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, defaultValue?: string) => translations[key] || defaultValue || key,
  }),
  Tolgee: () => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
  }),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => children,
  T: ({ keyName, defaultValue }: { keyName: string; defaultValue?: string }) =>
    defaultValue || keyName,
  useTolgee: () => ({}),
  BackendProvider: ({ children }: { children: React.ReactNode }) => children,
  FormatSimple: () => ({}),
}));

describe('FailedToUpload', () => {
  const defaultProps = {
    imageUrl: 'https://example.com/error-image.png',
    title: 'File size too large',
    description: 'The file you uploaded exceeds the maximum allowed size.',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders with all required props', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      // Check if the error container is rendered
      expect(screen.getByTestId('error-container')).toBeInTheDocument();

      // Check if the image is rendered (CustomImage renders as img tag)
      const image = screen.getByAltText('logo');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src');

      // Check if title and description are rendered
      expect(screen.getByTestId('max-file-size-error')).toHaveTextContent('File size too large');
      expect(screen.getByTestId('upload-failed-error')).toHaveTextContent(
        'The file you uploaded exceeds the maximum allowed size.'
      );

      // Check if reset button is rendered
      expect(screen.getByTestId('csv-file-info-reset-button')).toHaveTextContent('Reset Upload');
    });

    it('renders without onReset prop', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      // Component should still render without onReset
      expect(screen.getByTestId('error-container')).toBeInTheDocument();
      expect(screen.getByAltText('logo')).toBeInTheDocument();
      expect(screen.getByTestId('max-file-size-error')).toBeInTheDocument();
      expect(screen.getByTestId('upload-failed-error')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-reset-button')).toBeInTheDocument();
    });

    it('renders with custom title and description', () => {
      const customProps = {
        ...defaultProps,
        title: 'Custom Error Title',
        description: 'This is a custom error description with special characters: !@#$%^&*()',
      };

      renderWithMantine(<FailedToUpload {...customProps} />);

      expect(screen.getByTestId('max-file-size-error')).toHaveTextContent('Custom Error Title');
      expect(screen.getByTestId('upload-failed-error')).toHaveTextContent(
        'This is a custom error description with special characters: !@#$%^&*()'
      );
    });

    it('renders with different image URL', () => {
      const customProps = {
        ...defaultProps,
        imageUrl: 'https://example.com/custom-error-image.jpg',
      };

      renderWithMantine(<FailedToUpload {...customProps} />);

      const image = screen.getByAltText('logo');
      expect(image).toBeInTheDocument();
    });

    it('renders with empty strings for title and description', () => {
      const emptyProps = {
        ...defaultProps,
        title: '',
        description: '',
      };

      renderWithMantine(<FailedToUpload {...emptyProps} />);

      expect(screen.getByTestId('max-file-size-error')).toHaveTextContent('');
      expect(screen.getByTestId('upload-failed-error')).toHaveTextContent('');
    });
  });

  describe('Button Interactions', () => {
    it('calls onReset when reset button is clicked', () => {
      const mockOnReset = vi.fn();
      renderWithMantine(<FailedToUpload {...defaultProps} onReset={mockOnReset} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');
      fireEvent.click(resetButton);

      expect(mockOnReset).toHaveBeenCalledTimes(1);
    });

    it('does not throw error when reset button is clicked without onReset prop', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');

      // Should not throw error when clicked without onReset
      expect(() => {
        fireEvent.click(resetButton);
      }).not.toThrow();
    });

    it('calls onReset multiple times when button is clicked multiple times', () => {
      const mockOnReset = vi.fn();
      renderWithMantine(<FailedToUpload {...defaultProps} onReset={mockOnReset} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');

      fireEvent.click(resetButton);
      fireEvent.click(resetButton);
      fireEvent.click(resetButton);

      expect(mockOnReset).toHaveBeenCalledTimes(3);
    });
  });

  describe('Styling and Layout', () => {
    it('renders with correct container structure', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      const container = screen.getByTestId('error-container');

      // Check if the container is rendered
      expect(container).toBeInTheDocument();

      // Check if all child elements are present
      expect(screen.getByAltText('logo')).toBeInTheDocument();
      expect(screen.getByTestId('max-file-size-error')).toBeInTheDocument();
      expect(screen.getByTestId('upload-failed-error')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-reset-button')).toBeInTheDocument();
    });

    it('renders with proper spacing between elements', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      // All elements should be rendered in the correct order
      const container = screen.getByTestId('error-container');
      const image = screen.getByAltText('logo');
      const title = screen.getByTestId('max-file-size-error');
      const description = screen.getByTestId('upload-failed-error');
      const button = screen.getByTestId('csv-file-info-reset-button');

      expect(container).toContainElement(image);
      expect(container).toContainElement(title);
      expect(container).toContainElement(description);
      expect(container).toContainElement(button);
    });
  });

  describe('Accessibility', () => {
    it('has proper test IDs for testing and accessibility', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      // All interactive and important elements should have test IDs
      expect(screen.getByTestId('error-container')).toBeInTheDocument();
      expect(screen.getByAltText('logo')).toBeInTheDocument();
      expect(screen.getByTestId('max-file-size-error')).toBeInTheDocument();
      expect(screen.getByTestId('upload-failed-error')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-reset-button')).toBeInTheDocument();
    });

    it('renders button with correct variant and size', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');

      // The button should be a DecaButton with negative_text variant and sm size
      expect(resetButton).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles very long title and description', () => {
      const longProps = {
        ...defaultProps,
        title:
          'This is a very long error title that might wrap to multiple lines and should be handled gracefully by the component without breaking the layout or causing any visual issues',
        description:
          'This is an extremely long error description that contains a lot of text and might need to wrap to multiple lines. It should be handled properly by the component and maintain proper spacing and alignment. The text should remain readable and the layout should not break.',
      };

      renderWithMantine(<FailedToUpload {...longProps} />);

      expect(screen.getByTestId('max-file-size-error')).toHaveTextContent(longProps.title);
      expect(screen.getByTestId('upload-failed-error')).toHaveTextContent(longProps.description);
    });

    it('handles special characters in title and description', () => {
      const specialProps = {
        ...defaultProps,
        title: 'Error with special chars: !@#$%^&*()_+-=[]{}|;:,.<>?',
        description:
          'Description with quotes: "Hello" and \'World\' and <script>alert("test")</script>',
      };

      renderWithMantine(<FailedToUpload {...specialProps} />);

      expect(screen.getByTestId('max-file-size-error')).toHaveTextContent(specialProps.title);
      expect(screen.getByTestId('upload-failed-error')).toHaveTextContent(specialProps.description);
    });

    it('handles unicode characters in title and description', () => {
      const unicodeProps = {
        ...defaultProps,
        title: 'エラー: ファイルサイズが大きすぎます',
        description: 'アップロードしたファイルが最大サイズを超えています。',
      };

      renderWithMantine(<FailedToUpload {...unicodeProps} />);

      expect(screen.getByTestId('max-file-size-error')).toHaveTextContent(unicodeProps.title);
      expect(screen.getByTestId('upload-failed-error')).toHaveTextContent(unicodeProps.description);
    });

    it('handles different image URL formats', () => {
      const imageUrls = [
        'https://example.com/image.png',
        'https://example.com/image.jpg',
        'https://example.com/image.svg',
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCI+PC9zdmc+',
        '/relative/path/to/image.png',
      ];

      imageUrls.forEach((imageUrl) => {
        const props = { ...defaultProps, imageUrl };
        const { unmount } = renderWithMantine(<FailedToUpload {...props} />);

        const image = screen.getByAltText('logo');
        expect(image).toBeInTheDocument();

        unmount();
      });
    });
  });

  describe('Component Integration', () => {
    it('works correctly with Mantine theme', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      // Component should render without theme-related errors
      expect(screen.getByTestId('error-container')).toBeInTheDocument();
    });

    it('translates button text correctly', () => {
      renderWithMantine(<FailedToUpload {...defaultProps} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');
      expect(resetButton).toHaveTextContent('Reset Upload');
    });

    it('handles missing translation gracefully', () => {
      // Temporarily remove the translation
      const originalTranslation = translations.resetUpload;
      delete translations.resetUpload;

      renderWithMantine(<FailedToUpload {...defaultProps} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');
      expect(resetButton).toHaveTextContent('resetUpload'); // Should fall back to key

      // Restore the translation
      translations.resetUpload = originalTranslation;
    });
  });
});
