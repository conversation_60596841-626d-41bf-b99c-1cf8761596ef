import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import SampleDownload from './SampleDownload';

// Mock useSetDefaultLang hook
const mockUseSetDefaultLang = vi.hoisted(() => vi.fn(() => 'en'));
vi.mock('@resola-ai/ui/hooks/useSetDefaultLang', () => ({
  useSetDefaultLang: mockUseSetDefaultLang,
}));

// Mock DecaButton component
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} data-testid='deca-button' {...props}>
      {children}
    </button>
  ),
}));

// Mock translation
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: vi.fn((key) => key) }),
}));

// Mock URL APIs globally
const mockCreateObjectURL = vi.fn();
const mockRevokeObjectURL = vi.fn();
const mockConsoleError = vi.fn();

describe('SampleDownload', () => {
  beforeEach(() => {
    // Setup URL mocks
    global.URL.createObjectURL = mockCreateObjectURL;
    global.URL.revokeObjectURL = mockRevokeObjectURL;
    mockCreateObjectURL.mockReturnValue('blob:mock-url');

    // Mock console.error
    global.console.error = mockConsoleError;

    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Component rendering', () => {
    it('should render download button', () => {
      renderWithMantine(<SampleDownload />);

      const downloadButton = screen.getByTestId('deca-button');
      expect(downloadButton).toBeInTheDocument();
      expect(downloadButton).not.toBeDisabled();
    });
  });

  describe('Download functionality', () => {
    it('should handle download with proper URL creation', () => {
      // Mock DOM APIs after rendering but before interaction
      const mockLink = {
        setAttribute: vi.fn(),
        click: vi.fn(),
        style: {},
      };

      const originalCreateElement = document.createElement;
      const originalAppendChild = document.body.appendChild;
      const originalRemoveChild = document.body.removeChild;

      renderWithMantine(<SampleDownload />);

      // Now mock the DOM APIs for the download operation
      document.createElement = vi.fn().mockReturnValue(mockLink);
      document.body.appendChild = vi.fn();
      document.body.removeChild = vi.fn();

      const downloadButton = screen.getByTestId('deca-button');
      fireEvent.click(downloadButton);

      // Verify the download process
      expect(mockCreateObjectURL).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'text/csv;charset=utf-8;',
        })
      );
      expect(mockLink.setAttribute).toHaveBeenCalledWith('href', 'blob:mock-url');
      expect(mockLink.setAttribute).toHaveBeenCalledWith('download', 'example_people.csv');
      expect(mockRevokeObjectURL).toHaveBeenCalledWith('blob:mock-url');

      // Restore original methods
      document.createElement = originalCreateElement;
      document.body.appendChild = originalAppendChild;
      document.body.removeChild = originalRemoveChild;
    });

    it('should handle Japanese locale correctly', () => {
      mockUseSetDefaultLang.mockReturnValue('ja');

      const mockLink = {
        setAttribute: vi.fn(),
        click: vi.fn(),
        style: {},
      };

      const originalCreateElement = document.createElement;
      const originalAppendChild = document.body.appendChild;
      const originalRemoveChild = document.body.removeChild;

      renderWithMantine(<SampleDownload />);

      // Mock DOM APIs for download
      document.createElement = vi.fn().mockReturnValue(mockLink);
      document.body.appendChild = vi.fn();
      document.body.removeChild = vi.fn();

      const downloadButton = screen.getByTestId('deca-button');
      fireEvent.click(downloadButton);

      expect(mockLink.setAttribute).toHaveBeenCalledWith('download', 'example_people_ja.csv');

      // Restore original methods
      document.createElement = originalCreateElement;
      document.body.appendChild = originalAppendChild;
      document.body.removeChild = originalRemoveChild;
    });
  });

  describe('Error handling', () => {
    it('should handle download errors gracefully', () => {
      // Make createObjectURL throw an error
      mockCreateObjectURL.mockImplementation(() => {
        throw new Error('Download failed');
      });

      renderWithMantine(<SampleDownload />);

      const downloadButton = screen.getByTestId('deca-button');
      fireEvent.click(downloadButton);

      expect(mockConsoleError).toHaveBeenCalledWith('Download failed:', expect.any(Error));
    });
  });

  describe('CSV content validation', () => {
    it('should create blob with correct content type', () => {
      renderWithMantine(<SampleDownload />);

      const downloadButton = screen.getByTestId('deca-button');
      fireEvent.click(downloadButton);

      // Verify blob creation
      expect(mockCreateObjectURL).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'text/csv;charset=utf-8;',
        })
      );

      const blobCall = mockCreateObjectURL.mock.calls[0][0];
      expect(blobCall).toBeInstanceOf(Blob);
      expect(blobCall.type).toBe('text/csv;charset=utf-8;');
    });
  });

  describe('Component integration', () => {
    it('should integrate with translation system', () => {
      renderWithMantine(<SampleDownload />);

      const downloadButton = screen.getByTestId('deca-button');
      expect(downloadButton).toBeInTheDocument();

      // Button should display translated text
      expect(downloadButton).toHaveTextContent('sampleFile');
    });

    it('should work with different locales', () => {
      const locales = ['en', 'ja'];

      locales.forEach((locale) => {
        mockUseSetDefaultLang.mockReturnValue(locale);

        const { unmount } = renderWithMantine(<SampleDownload />);

        const downloadButton = screen.getByTestId('deca-button');
        expect(downloadButton).toBeInTheDocument();

        unmount();
      });
    });

    it('should handle button states correctly', () => {
      renderWithMantine(<SampleDownload />);

      const downloadButton = screen.getByTestId('deca-button');

      // Button should not be disabled initially
      expect(downloadButton).not.toBeDisabled();

      // Click should not throw errors
      expect(() => {
        fireEvent.click(downloadButton);
      }).not.toThrow();
    });
  });
});
