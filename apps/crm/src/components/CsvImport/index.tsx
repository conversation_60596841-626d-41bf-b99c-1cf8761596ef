import { useAppContext } from '@/contexts/AppContext';
import { TasksAPI } from '@/services/api';
import { Box, Group, rem, Stack, Text } from '@mantine/core';
import type { FileWithPath } from '@mantine/dropzone';
import { createStyles } from '@mantine/emotion';
import { DecaButton, Modal } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { DEFAULT_PROFILE_SETTINGS, NOT_ALLOW_IMPORT_FIELDS } from '../../constants/workspace';
import { useCsvParser, useObject } from '../../hooks';
import type { IImportCSV } from '../../models';
import type { FieldMapping } from '../../utils';
import {
  createFieldPayload,
  createFieldsToConfirmMap,
  getActiveFieldMappings,
  getCsvFieldObjectConflicts,
  normalizeFieldMappings,
} from '../../utils/csvImportUtils';
import { useNavigateObject } from '../NavBar/NavigationControls/useNavigateObject';
import CsvDropzone from './CsvDropzone';
import CsvFieldMapping from './CsvFieldMapping';
import CsvFileInfo from './CsvFileInfo';
import CsvObjectSelection from './CsvObjectSelection';
import CsvSteps from './CsvSteps';
import ObjectPreview from './ObjectPreview';
import SampleDownload from './SampleDownload';

const useStyles = createStyles((theme) => ({
  modal: {
    '.mantine-Modal-content': {
      height: '80vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      '.mantine-Modal-body': {
        height: `calc(100% - ${rem(60)})`,
        '> div': {
          marginRight: rem(-8),
          marginLeft: rem(-8),
          height: '100%',
        },
      },
    },
  },
  dropzoneContainer: {
    marginBottom: rem(24),
  },
  modalContent: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
  },
  scrollableBody: {
    flex: 1,
    overflowY: 'auto',
    paddingRight: rem(8),
  },
  footerButtons: {
    borderTop: `1px solid ${theme.colors.decaLight[3]}`,
    padding: `${rem(16)} ${rem(24)} ${rem(0)} ${rem(24)}`,
    marginRight: rem(-16),
    marginLeft: rem(-16),
    position: 'sticky',
    bottom: 0,
    zIndex: 1,
  },
}));

interface CsvImportProps {
  opened: boolean;
  onClose: () => void;
}

const CsvImport: React.FC<CsvImportProps> = ({ opened, onClose }) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const { wsId, id } = useParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [uploadedFile, setUploadedFile] = useState<FileWithPath | null>(null);
  const [isDragHover, setIsDragHover] = useState(false);
  const [selectedObjectType, setSelectedObjectType] = useState<string>('');
  const [importMode, setImportMode] = useState<'existing' | 'new'>('existing');
  const [newObjectName, setNewObjectName] = useState('');
  const [useHeaders, setUseHeaders] = useState(true);
  const [fieldMappings, setFieldMappings] = useState<FieldMapping[]>([]);
  const [hasUploadError, setHasUploadError] = useState(false);
  const [isCsvValid, setIsCsvValid] = useState(false);
  const { csvData, isLoadingCsv, isUploading, uploadProgress } = useCsvParser(uploadedFile);
  const { object: selectedObject, mutate: mutateObject } = useObject(
    wsId,
    importMode === 'existing' ? selectedObjectType : null,
    true
  );
  const { mutateObjects, setImportLoading, setReloadObject } = useAppContext();
  const { handleNavigateObject } = useNavigateObject();

  const steps = [
    { number: 1, label: t('uploadFile', 'Upload file') },
    { number: 2, label: t('selectObject', 'Select object') },
    { number: 3, label: t('matchingFields', 'Matching fields') },
    { number: 4, label: t('preview', 'Preview') },
  ];

  const handleFileDrop = (files: FileWithPath[]) => {
    if (files.length > 0) {
      setUploadedFile(files[0]);
      setHasUploadError(false);
    }
    setIsDragHover(false);
  };

  const handleDragEnter = () => {
    setIsDragHover(true);
  };

  const handleDragLeave = () => {
    setIsDragHover(false);
  };

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCancel = () => {
    setCurrentStep(1);
    setUploadedFile(null);
    setSelectedObjectType('');
    setImportMode('existing');
    setNewObjectName('');
    setUseHeaders(true);
    setFieldMappings([]);
    onClose();
  };

  const handleFileReset = () => {
    setUploadedFile(null);
    setFieldMappings([]);
    setHasUploadError(false); // Reset error state when file is reset
    setIsCsvValid(false); // Reset validation state when file is reset
  };

  const handleUploadReset = () => {
    setHasUploadError(false);
    setUploadedFile(null);
    setIsCsvValid(false);
  };

  const handleFileReject = () => {
    setHasUploadError(true);
  };

  const handleObjectTypeChange = (objectType: string) => {
    setSelectedObjectType(objectType);
    setFieldMappings([]); // Reset field mappings when object type changes
  };

  const handleImportModeChange = (mode: 'existing' | 'new') => {
    setImportMode(mode);
    setFieldMappings([]); // Reset field mappings when import mode changes
  };

  const handleUseHeadersChange = (useHeadersValue: boolean) => {
    setUseHeaders(useHeadersValue);
    setFieldMappings([]); // Reset field mappings when header setting changes
  };

  const handleNewObjectNameChange = (name: string) => {
    setNewObjectName(name);
  };

  const handleCsvValidationChange = (isValid: boolean) => {
    setIsCsvValid(isValid);
  };

  const handleFieldMappingChange = (mappings: FieldMapping[]) => {
    setFieldMappings(mappings);
  };

  const onImportComplete = async (objectId: string) => {
    await mutateObjects();
    setReloadObject(objectId);
    if (objectId === id) {
      mutateObject();
    } else {
      handleNavigateObject(objectId);
    }
  };

  const handleImport = async () => {
    const isExistingImport = importMode === 'existing';
    // Create field payload using utility function
    const fieldCreatePayload = createFieldPayload(fieldMappings);

    // Determine object name based on import mode
    const objectName = isExistingImport
      ? selectedObject?.name?.singular || selectedObjectType
      : newObjectName;

    const importPayload: IImportCSV = {
      filename: uploadedFile?.name ?? '',
      size: uploadedFile?.size ?? 0,
      objectId: isExistingImport ? (selectedObject?.id ?? '') : undefined,
      objectMeta: {
        name: {
          singular: objectName,
          plural: objectName,
        },
        icon: 'users',
        profileSettings: DEFAULT_PROFILE_SETTINGS as any,
        fields: normalizeFieldMappings(fieldCreatePayload) as any,
      },
    };
    handleCancel();
    setImportLoading(true);

    try {
      // Initiate the import
      const response = await TasksAPI.importCSV(wsId ?? '', importPayload);
      const { taskId, uploadUrl, objectId, fields } = response;

      // Upload the file to the provided URL
      await fetch(uploadUrl, {
        method: 'PUT',
        body: uploadedFile ?? new File([], ''),
      });

      // Map fields to CSV columns using utility function
      const activeFieldMappings = getActiveFieldMappings(fieldMappings);
      const fieldsToConfirm = createFieldsToConfirmMap(activeFieldMappings, fields);

      // Confirm the import after the file is uploaded
      await TasksAPI.confirmImport(wsId ?? '', taskId, objectId, fieldsToConfirm);

      // api polling to get the status of the import task
      const interval = setInterval(async () => {
        const status = await TasksAPI.getTaskStatus(wsId ?? '', taskId);
        if (['completed', 'failed'].includes(status.status)) {
          clearInterval(interval);
          setImportLoading(false);
          onImportComplete(objectId);
        }
      }, 5000);
    } catch (error) {
      console.error('Import failed:', error);
    }
  };

  const objectFields = useMemo(() => {
    return (
      selectedObject?.fields?.filter(
        (field) => !NOT_ALLOW_IMPORT_FIELDS.includes(field.type as any)
      ) || []
    );
  }, [selectedObject]);

  // Validate field mappings for step 3
  const fieldMappingValidation = useMemo(() => {
    if (currentStep === 3 && fieldMappings.length > 0) {
      // Only check for conflicts when creating new fields (not when mapping to existing fields or skipping)
      const newFieldMappings = fieldMappings.filter(
        (mapping) => !mapping.isExistingField && !mapping.skipped
      );
      const conflicts = getCsvFieldObjectConflicts(newFieldMappings, objectFields);
      const hasConflicts = conflicts.length > 0;

      return {
        isValid: !hasConflicts,
        errors: hasConflicts ? ['csvFieldConflictsWithObjectField'] : [],
      };
    }
    return { isValid: true, errors: [] };
  }, [currentStep, fieldMappings, objectFields]);

  const renderUploadStep = () => (
    <Stack gap={rem(16)} mt={rem(16)}>
      <Text fz={rem(14)}>
        {t('dragAndDropText')}
        <SampleDownload />
      </Text>

      <div className={classes.dropzoneContainer}>
        {!uploadedFile && (
          <CsvDropzone
            onFileDrop={handleFileDrop}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            isDragHover={isDragHover}
            hasError={hasUploadError}
            onReset={handleUploadReset}
            onReject={handleFileReject}
          />
        )}
        {uploadedFile && (
          <CsvFileInfo
            uploadedFile={uploadedFile}
            onReset={handleFileReset}
            csvData={csvData}
            isLoading={isLoadingCsv}
            isUploading={isUploading}
            uploadProgress={uploadProgress}
            onValidationChange={handleCsvValidationChange}
          />
        )}
      </div>
    </Stack>
  );

  const renderCurrentStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderUploadStep();
      case 2:
        return (
          <CsvObjectSelection
            csvData={csvData}
            selectedObjectType={selectedObjectType}
            importMode={importMode}
            newObjectName={newObjectName}
            useHeaders={useHeaders}
            onObjectTypeChange={handleObjectTypeChange}
            onImportModeChange={handleImportModeChange}
            onUseHeadersChange={handleUseHeadersChange}
            onNewObjectNameChange={handleNewObjectNameChange}
          />
        );
      case 3: {
        const objectName =
          importMode === 'existing'
            ? selectedObject?.name?.singular || selectedObjectType
            : newObjectName;

        return (
          <CsvFieldMapping
            csvData={csvData}
            onFieldMappingChange={handleFieldMappingChange}
            fileName={uploadedFile?.name}
            objectName={objectName}
            objectFields={objectFields}
            fieldMappings={fieldMappings}
          />
        );
      }
      case 4:
        return <ObjectPreview csvData={csvData} fieldMappings={fieldMappings} />;
      default:
        return null;
    }
  };

  const getButtonText = () => {
    return currentStep === 4 ? t('import') : t('next');
  };

  const handleButtonClick = () => {
    if (currentStep === 4) {
      handleImport();
    } else {
      handleNext();
    }
  };

  return (
    <Modal
      centered
      opened={opened}
      onClose={onClose}
      title={t('uploadCsvFile')}
      size={rem(1200)}
      className={classes.modal}
    >
      <div className={classes.modalContent}>
        <Box className={classes.scrollableBody}>
          <Stack>
            <CsvSteps steps={steps} currentStep={currentStep} />
            {renderCurrentStepContent()}
          </Stack>
        </Box>
        <Group justify='space-between' className={classes.footerButtons}>
          <DecaButton variant='neutral' onClick={handleCancel}>
            {t('cancel')}
          </DecaButton>
          <Group>
            <DecaButton variant='neutral_text' onClick={handleBack} disabled={currentStep === 1}>
              {t('back')}
            </DecaButton>
            <DecaButton
              onClick={handleButtonClick}
              disabled={
                (currentStep === 1 && (!uploadedFile || isUploading || !isCsvValid)) ||
                (currentStep === 2 && importMode === 'existing' && !selectedObjectType) ||
                (currentStep === 2 && importMode === 'new' && !newObjectName.trim()) ||
                (currentStep === 3 && getActiveFieldMappings(fieldMappings).length === 0) ||
                (currentStep === 3 && !fieldMappingValidation.isValid)
              }
            >
              {getButtonText()}
            </DecaButton>
          </Group>
        </Group>
      </div>
    </Modal>
  );
};

export default CsvImport;
