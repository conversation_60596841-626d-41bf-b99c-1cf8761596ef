import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useImportStyles = createStyles((theme) => ({
  // Common container styles
  container: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100%',
  },

  // Common bordered container (used in CsvObjectSelection)
  borderedContainer: {
    marginTop: rem(24),
    padding: rem(24),
    border: `${rem(1)} dashed ${theme.colors.decaLight[4]}`,
    borderRadius: rem(4),
  },

  // Common title styles
  title: {
    fontSize: rem(14),
    fontWeight: 400,
    color: theme.colors.decaGrey[6],
    marginBottom: rem(32),
  },

  // CSV Field Mapping specific styles
  mappingContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(8),
    width: '100%',
    maxWidth: rem(800),
  },
  mappingHeader: {
    display: 'flex',
    fontWeight: 500,
    fontSize: rem(14),
    alignItems: 'center',
    marginBottom: rem(10),
  },
  headerColumn: {
    '&:first-of-type': {
      width: rem(200),
      marginRight: rem(16),
      flexShrink: 0,
    },
    '&:last-of-type': {
      flex: 1,
    },
  },
  mappingRow: {
    display: 'flex',
    borderRadius: rem(4),
    alignItems: 'flex-start',
    backgroundColor: 'transparent',
  },
  csvFieldColumn: {
    width: rem(200),
    marginRight: rem(16),
    display: 'flex',
    alignItems: 'flex-start',
    paddingTop: rem(4),
    flexShrink: 0,
  },
  arrow: {
    marginRight: rem(16),
    color: theme.colors.decaGrey[5],
    fontSize: rem(16),
    width: rem(20),
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'center',
    paddingTop: rem(4),
    flexShrink: 0,
  },
  crmFieldColumn: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    minWidth: 0, // Allow shrinking
  },
  selectRow: {
    display: 'flex',
    gap: rem(8),
    width: '100%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    alignSelf: 'flex-start',
    minWidth: rem(420),

    '.select-error': {
      marginTop: rem(0),
      marginBottom: rem(6),
    },
  },
  csvFieldName: {
    fontWeight: 500,
    color: theme.colors.decaGrey[7],
  },
  fieldTypeSelect: {
    flex: 1,
    minWidth: rem(150),
    maxWidth: rem(220),
  },
  fieldTypeSelectSecondary: {
    flex: 1,
    minWidth: rem(150),
    maxWidth: rem(220),
  },
  fieldTypeSelectFull: {
    width: '100%',
    alignSelf: 'flex-start',
  },

  options: {
    height: rem(280),
  },

  // CSV Object Selection specific styles
  radioGroup: {
    marginBottom: rem(32),
  },
  radioStack: {
    gap: rem(16),
  },
  radioItem: {
    height: rem(78),
    border: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    borderRadius: rem(8),
    padding: rem(16),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    '&:hover': {
      borderColor: theme.colors.decaLight[2],
    },
    '&[data-selected="true"]': {
      backgroundColor: theme.colors.decaLight[0],
    },
  },
  radioContent: {
    display: 'flex',
    alignItems: 'center',
    flex: 1,
  },
  radioControl: {
    minWidth: rem(200),
    marginLeft: rem(16),
  },
  columnsPreview: {
    display: 'flex',
    gap: rem(8),
    flexWrap: 'wrap',
    marginTop: rem(0),
  },
  columnTag: {
    padding: `${rem(6)} ${rem(12)}`,
    backgroundColor: theme.colors.decaLight[0],
    border: `1px solid ${theme.colors.decaLight[3]}`,
    borderRadius: rem(4),
    fontSize: rem(12),
    color: theme.colors.decaGrey[7],
  },

  // CSV Preview specific styles
  csvPreview: {
    flex: 1,
    border: `1px solid ${theme.colors.decaLight[3]}`,
    borderRadius: rem(8),
    overflow: 'hidden',
    maxHeight: rem(300),
  },
  csvTable: {
    border: `1px solid ${theme.colors.decaLight[3]}`,
    '& thead th': {
      backgroundColor: theme.colors.decaLight[1],
      fontWeight: 500,
      fontSize: rem(12),
      padding: `${rem(4)} ${rem(8)}`,
      border: `1px solid ${theme.colors.decaLight[3]}`,
      minWidth: rem(60),
      maxWidth: rem(100),
      textAlign: 'center',
      '&:first-of-type': {
        minWidth: rem(50),
        maxWidth: rem(60),
      },
    },
    '& tbody td': {
      fontSize: rem(12),
      padding: `${rem(4)} ${rem(8)}`,
      border: `1px solid ${theme.colors.decaLight[3]}`,
      minWidth: rem(80),
      maxWidth: rem(120),
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
      '&:first-of-type': {
        minWidth: rem(40),
        maxWidth: rem(60),
        backgroundColor: theme.colors.decaLight[1],
        textAlign: 'center',
      },
    },
  },
}));
