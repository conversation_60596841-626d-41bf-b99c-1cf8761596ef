import { Box, Flex, Group, Text, rem } from '@mantine/core';
import type { FileWithPath } from '@mantine/dropzone';
import { CustomImage, DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import CsvPreview from './CsvPreview';
import CsvUploadProgress from './CsvUploadProgress';
import FailedToUpload from './FailedToUpload';

interface CsvFileInfoProps {
  uploadedFile: FileWithPath;
  onReset: () => void;
  csvData: string[][];
  isLoading: boolean;
  isUploading: boolean;
  uploadProgress: number;
  onValidationChange?: (isValid: boolean) => void;
}

const CsvFileInfo: React.FC<CsvFileInfoProps> = ({
  uploadedFile,
  onReset,
  csvData,
  isLoading,
  isUploading,
  uploadProgress,
  onValidationChange,
}) => {
  const { t } = useTranslate();

  const duplicateFieldInfo = React.useMemo(() => {
    if (csvData.length === 0) return { hasDuplicates: false, duplicateNames: [] };

    const headers = csvData[0];
    const seen = new Set();
    const duplicates = new Set();

    headers.forEach((header) => {
      if (seen.has(header)) {
        duplicates.add(header);
      } else {
        seen.add(header);
      }
    });

    return {
      hasDuplicates: duplicates.size > 0,
      duplicateNames: Array.from(duplicates),
    };
  }, [csvData]);

  React.useEffect(() => {
    if (onValidationChange) {
      const isValid = !duplicateFieldInfo.hasDuplicates && csvData.length > 0 && !isUploading;
      onValidationChange(isValid);
    }
  }, [duplicateFieldInfo.hasDuplicates, csvData.length, isUploading, onValidationChange]);

  if (isUploading || csvData.length === 0) {
    return (
      <Box data-testid='csv-file-info-uploading'>
        <CsvUploadProgress fileName={uploadedFile.name} progress={uploadProgress} />
      </Box>
    );
  }

  if (duplicateFieldInfo.hasDuplicates) {
    return (
      <FailedToUpload
        onReset={onReset}
        imageUrl={'images/invalid_csv.png'}
        title={t('invalidCsv')}
        description={t('invalidCsvDesc', { names: duplicateFieldInfo.duplicateNames.join(', ') })}
      />
    );
  }

  return (
    <Box w={'100%'} data-testid='csv-file-info-container'>
      <Flex data-testid='csv-file-info-content'>
        <Group w={rem(500)} justify='center' align='center' data-testid='csv-file-info-header'>
          <CustomImage url={'images/csv_import.png'} data-testid='csv-file-info-image' />
          <Text data-testid='csv-file-info-filename'>{uploadedFile.name}</Text>
        </Group>
        <CsvPreview csvData={csvData} isLoading={isLoading} data-testid='csv-file-info-preview' />
      </Flex>

      <Box
        w={'100%'}
        ta='center'
        mt={rem(50)}
        pos='absolute'
        bottom={rem(90)}
        data-testid='csv-file-info-actions'
      >
        <DecaButton
          variant='negative_text'
          size='sm'
          onClick={onReset}
          data-testid='csv-file-info-reset-button'
        >
          {t('resetUpload')}
        </DecaButton>
      </Box>
    </Box>
  );
};

export default CsvFileInfo;
