import { Flex, rem, Stack, Text } from '@mantine/core';
import { Dropzone, type FileWithPath } from '@mantine/dropzone';
import { createStyles } from '@mantine/emotion';
import { CustomImage, DecaButton } from '@resola-ai/ui';
import { IconFilePlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import FailedToUpload from './FailedToUpload';

const useStyles = createStyles((theme) => ({
  dropzone: {
    height: rem(366),
    border: `${rem(2)} dashed ${theme.colors.decaLight[4]}`,
    borderRadius: rem(8),
    padding: rem(30),
    backgroundColor: theme.colors.decaLight[0],
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    '&:hover': {
      borderStyle: 'solid',
      boxShadow: `0 0 ${rem(4)} ${rem(0)} ${theme.colors.decaBlue[5]}`,
      borderColor: theme.colors.decaNavy[4],
      backgroundColor: theme.colors.decaBlue[0],
    },
    '&[data-accept]': {
      borderColor: theme.colors.decaBlue[6],
      backgroundColor: theme.colors.decaBlue[0],
    },
    '&[data-reject]': {
      borderColor: theme.colors.decaRed[6],
      backgroundColor: theme.colors.decaRed[0],
    },
    '& .mantine-Dropzone-inner': {
      height: '100%',
    },
  },
}));

interface CsvDropzoneProps {
  onFileDrop: (files: FileWithPath[]) => void;
  onDragEnter: () => void;
  onDragLeave: () => void;
  isDragHover: boolean;
  hasError?: boolean;
  onReset?: () => void;
  onReject?: (files: any[]) => void;
}

const CsvDropzone: React.FC<CsvDropzoneProps> = ({
  onFileDrop,
  onDragEnter,
  onDragLeave,
  isDragHover,
  hasError = false,
  onReset,
  onReject,
}) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();

  if (hasError) {
    return (
      <FailedToUpload
        onReset={onReset}
        imageUrl={'images/csv_import_failed.png'}
        title={t('maxFileSize')}
        description={t('fileUploadFailed')}
      />
    );
  }

  return (
    <Dropzone
      onDrop={onFileDrop}
      onReject={onReject}
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      accept={{ 'text/csv': ['.csv'] }}
      maxSize={5 * 1024 * 1024} // 5MB
      maxFiles={1}
      className={classes.dropzone}
      data-testid='csv-dropzone'
    >
      <Stack align='center' gap={rem(16)} h={'100%'} data-testid='dropzone-content'>
        {isDragHover ? (
          <Flex
            direction='column'
            fz={rem(14)}
            c='decaNavy.5'
            h={'100%'}
            align='center'
            justify='center'
            gap={rem(16)}
            data-testid='drag-hover-content'
          >
            <IconFilePlus size={20} data-testid='file-plus-icon' />
            <Text data-testid='drop-file-text'>{t('dropFileHere')}</Text>
          </Flex>
        ) : (
          <>
            <CustomImage url={'images/csv_import.png'} data-testid='csv-import-image' />
            <DecaButton variant='neutral' onClick={() => {}} data-testid='upload-button'>
              {t('uploadFile')}
            </DecaButton>
            <Text ta='center' fz={rem(12)} c='decaGrey.5' data-testid='max-file-size-text'>
              {t('maxFileSize')}
            </Text>
          </>
        )}
      </Stack>
    </Dropzone>
  );
};

export default CsvDropzone;
