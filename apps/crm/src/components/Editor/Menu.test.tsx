import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import '@testing-library/jest-dom';
import type { MessageType } from '@/constants/workspace';
import EditorMenu from './Menu';
import type { Template } from './EditorConfig';

// Mock dependencies
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => ({
    object: {
      id: 'test-object-id',
      permission: {
        OBJECT_MANAGE_TEMPLATE: true,
      },
    },
  }),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    i18n: {
      language: 'en',
    },
  }),
}));

vi.mock('@emoji-mart/react', () => ({
  default: ({ onEmojiSelect }: { onEmojiSelect: (emoji: any) => void }) => (
    <div data-testid='emoji-picker'>
      <button onClick={() => onEmojiSelect({ native: '😀' })} data-testid='emoji-button'>
        😀
      </button>
    </div>
  ),
}));

vi.mock('@emoji-mart/data', () => ({
  default: {},
}));

vi.mock('@emoji-mart/data/i18n/en.json', () => ({
  default: {
    search: 'Search',
    clear: 'Clear',
    notfound: 'No emoji found',
    categories: {
      activity: 'Activity',
      people: 'Smileys & People',
      nature: 'Animals & Nature',
      foods: 'Food & Drink',
      places: 'Travel & Places',
      objects: 'Objects',
      symbols: 'Symbols',
      flags: 'Flags',
      custom: 'Custom',
    },
  },
}));

vi.mock('@emoji-mart/data/i18n/ja.json', () => ({
  default: {
    search: '検索',
    clear: 'クリア',
    notfound: '絵文字が見つかりません',
    categories: {
      activity: 'アクティビティ',
      people: 'スマイリーと人々',
      nature: '動物と自然',
      foods: '食べ物と飲み物',
      places: '旅行と場所',
      objects: 'オブジェクト',
      symbols: 'シンボル',
      flags: 'フラグ',
      custom: 'カスタム',
    },
  },
}));

vi.mock('./EditorTemplate', () => ({
  default: ({ opened, onClose, templates, mutateTemplates }: any) => (
    <div data-testid='template-modal' data-opened={opened}>
      <button onClick={onClose} data-testid='close-template-modal'>
        Close
      </button>
      <div data-testid='template-count'>{templates.length}</div>
      <button onClick={mutateTemplates} data-testid='mutate-templates'>
        Mutate
      </button>
    </div>
  ),
}));

vi.mock('@resola-ai/ui/components/DecaTable/utils', () => ({
  PERMISSION_KEYS: {
    OBJECT_MANAGE_TEMPLATE: 'OBJECT_MANAGE_TEMPLATE',
  },
  isPermissionAllowed: (permissions: any, key: string) => {
    return permissions[key] === true;
  },
}));

describe('EditorMenu Component', () => {
  const mockOnTemplateSelect = vi.fn();
  const mockMutateTemplates = vi.fn();
  const mockOnEmojiSelect = vi.fn();
  const mockSetShowMenu = vi.fn();

  const defaultProps = {
    showMenu: false,
    setShowMenu: mockSetShowMenu,
    templates: [],
    onTemplateSelect: mockOnTemplateSelect,
    editorType: 'email' as MessageType,
    mutateTemplates: mockMutateTemplates,
  };

  const sampleTemplates: Template[] = [
    {
      id: '1',
      name: 'Welcome Email',
      description: 'Welcome new customers',
      content: '<p>Welcome to our service!</p>',
    },
    {
      id: '2',
      name: 'Follow-up Email',
      description: 'Follow-up with existing customers',
      content: '<p>Thank you for your business!</p>',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Line Editor Type (Emoji Picker)', () => {
    it('should render emoji picker for line editor type', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} editorType='line' onEmojiSelect={mockOnEmojiSelect} />
      );

      const emojiButton = screen.getAllByRole('button')[0]; // Get the emoji button
      expect(emojiButton).toBeInTheDocument();
    });

    it('should open emoji picker when button is clicked', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <EditorMenu {...defaultProps} editorType='line' onEmojiSelect={mockOnEmojiSelect} />
      );

      const emojiButton = screen.getByRole('button');
      await user.click(emojiButton);

      await waitFor(() => {
        expect(screen.getByTestId('emoji-picker')).toBeInTheDocument();
      });
    });

    it('should call onEmojiSelect when emoji is selected', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <EditorMenu {...defaultProps} editorType='line' onEmojiSelect={mockOnEmojiSelect} />
      );

      const emojiButton = screen.getByRole('button');
      await user.click(emojiButton);

      await waitFor(() => {
        expect(screen.getByTestId('emoji-picker')).toBeInTheDocument();
      });

      const specificEmojiButton = screen.getByTestId('emoji-button');
      await user.click(specificEmojiButton);

      expect(mockOnEmojiSelect).toHaveBeenCalledWith('😀');
    });

    it('should close emoji picker after selecting emoji', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <EditorMenu {...defaultProps} editorType='line' onEmojiSelect={mockOnEmojiSelect} />
      );

      const emojiButton = screen.getByRole('button');
      await user.click(emojiButton);

      await waitFor(() => {
        expect(screen.getByTestId('emoji-picker')).toBeInTheDocument();
      });

      const specificEmojiButton = screen.getByTestId('emoji-button');
      await user.click(specificEmojiButton);

      await waitFor(() => {
        expect(screen.queryByTestId('emoji-picker')).not.toBeInTheDocument();
      });
    });

    it('should not render template menu for line editor', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} editorType='line' templates={sampleTemplates} />
      );

      expect(screen.queryByText('Welcome Email')).not.toBeInTheDocument();
      expect(screen.queryByText('manageTemplates')).not.toBeInTheDocument();
    });
  });

  describe('Email/SMS Editor Types (Template Menu)', () => {
    it('should render menu target button for email editor', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} editorType='email' templates={sampleTemplates} />
      );

      const menuButton = screen.getAllByRole('button')[0]; // Get the first button (menu target)
      expect(menuButton).toBeInTheDocument();
      expect(menuButton).toHaveAttribute('aria-haspopup', 'menu');
    });

    it('should render menu target button for sms editor', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} editorType='sms' templates={sampleTemplates} />
      );

      const menuButton = screen.getAllByRole('button')[0]; // Get the first button (menu target)
      expect(menuButton).toBeInTheDocument();
      expect(menuButton).toHaveAttribute('aria-haspopup', 'menu');
    });

    it('should display templates when menu is open', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      expect(screen.getByText('Welcome Email')).toBeInTheDocument();
      expect(screen.getByText('Follow-up Email')).toBeInTheDocument();
      expect(screen.getByText('Welcome new customers')).toBeInTheDocument();
      expect(screen.getByText('Follow-up with existing customers')).toBeInTheDocument();
    });

    it('should call onTemplateSelect when template is clicked', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      const templateItem = screen.getByText('Welcome Email');
      await user.click(templateItem);

      expect(mockOnTemplateSelect).toHaveBeenCalledWith(sampleTemplates[0]);
    });

    it('should display manage templates option when user has permission', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      expect(screen.getByText('manageTemplates')).toBeInTheDocument();
    });

    it('should open template modal when manage templates is clicked', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      const manageButton = screen.getByText('manageTemplates');
      await user.click(manageButton);

      await waitFor(() => {
        const modal = screen.getByTestId('template-modal');
        expect(modal).toHaveAttribute('data-opened', 'true');
      });
    });

    it('should show divider when there are templates and user has manage permission', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      // Check if there's content before and after where divider would be
      expect(screen.getByText('Welcome Email')).toBeInTheDocument();
      expect(screen.getByText('manageTemplates')).toBeInTheDocument();
    });

    it('should not show divider when there are no templates', () => {
      renderWithMantine(<EditorMenu {...defaultProps} showMenu={true} templates={[]} />);

      // Only manage templates should be visible
      expect(screen.getByText('manageTemplates')).toBeInTheDocument();
      expect(screen.queryByText('Welcome Email')).not.toBeInTheDocument();
    });
  });

  describe('Template Modal Integration', () => {
    it('should pass correct props to template modal', () => {
      renderWithMantine(<EditorMenu {...defaultProps} templates={sampleTemplates} />);

      const modal = screen.getByTestId('template-modal');
      expect(modal).toHaveAttribute('data-opened', 'false');

      const templateCount = screen.getByTestId('template-count');
      expect(templateCount).toHaveTextContent('2');
    });

    it('should close template modal when onClose is called', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      // Open modal first
      const manageButton = screen.getByText('manageTemplates');
      await user.click(manageButton);

      await waitFor(() => {
        const modal = screen.getByTestId('template-modal');
        expect(modal).toHaveAttribute('data-opened', 'true');
      });

      // Close modal
      const closeButton = screen.getByTestId('close-template-modal');
      await user.click(closeButton);

      await waitFor(() => {
        const modal = screen.getByTestId('template-modal');
        expect(modal).toHaveAttribute('data-opened', 'false');
      });
    });

    it('should call mutateTemplates when template modal triggers mutation', async () => {
      const user = userEvent.setup();
      renderWithMantine(<EditorMenu {...defaultProps} templates={sampleTemplates} />);

      const mutateButton = screen.getByTestId('mutate-templates');
      await user.click(mutateButton);

      expect(mockMutateTemplates).toHaveBeenCalled();
    });
  });

  describe('Permission Handling', () => {
    it('should show manage templates when user has permission', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      // Should show manage templates with permission
      expect(screen.getByText('manageTemplates')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty template list gracefully', () => {
      renderWithMantine(<EditorMenu {...defaultProps} showMenu={true} templates={[]} />);

      expect(screen.getByText('manageTemplates')).toBeInTheDocument();
      expect(screen.queryByText('Welcome Email')).not.toBeInTheDocument();
    });

    it('should handle missing onEmojiSelect prop for line editor', () => {
      renderWithMantine(
        <EditorMenu
          {...defaultProps}
          editorType='line'
          // onEmojiSelect not provided
        />
      );

      const emojiButton = screen.getAllByRole('button')[0]; // Get the emoji button
      expect(emojiButton).toBeInTheDocument();
    });

    it('should handle template with missing description', () => {
      const templatesWithMissingDesc = [
        {
          id: '1',
          name: 'Test Template',
          description: '',
          content: '<p>Test content</p>',
        },
      ];

      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={templatesWithMissingDesc} />
      );

      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    it('should handle very long template names and descriptions', () => {
      const templatesWithLongText = [
        {
          id: '1',
          name: 'A'.repeat(100),
          description: 'B'.repeat(200),
          content: '<p>Test content</p>',
        },
      ];

      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={templatesWithLongText} />
      );

      expect(screen.getByText('A'.repeat(100))).toBeInTheDocument();
    });
  });

  describe('Menu State Management', () => {
    it('should call setShowMenu when menu state changes', () => {
      renderWithMantine(<EditorMenu {...defaultProps} showMenu={false} />);

      // The menu component should be set up to handle state changes
      expect(mockSetShowMenu).not.toHaveBeenCalled();
    });

    it('should properly handle menu open/close states', () => {
      // Test with menu closed
      const { unmount } = renderWithMantine(<EditorMenu {...defaultProps} showMenu={false} />);

      // Menu should not show templates when closed
      expect(screen.queryByText('Welcome Email')).not.toBeInTheDocument();

      unmount();

      // Test with menu open
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      // Menu should show templates when open
      expect(screen.getByText('Welcome Email')).toBeInTheDocument();
    });
  });

  describe('Icon Rendering', () => {
    it('should render email icon for email editor type', () => {
      renderWithMantine(<EditorMenu {...defaultProps} editorType='email' />);

      // The IconMail should be rendered (we can't easily test the actual icon,
      // but we can verify the button exists)
      const menuButton = screen.getAllByRole('button')[0];
      expect(menuButton).toBeInTheDocument();
    });

    it('should render message icon for sms editor type', () => {
      renderWithMantine(<EditorMenu {...defaultProps} editorType='sms' />);

      // The IconMessage should be rendered
      const menuButton = screen.getAllByRole('button')[0];
      expect(menuButton).toBeInTheDocument();
    });

    it('should render mood smile icon for line editor type', () => {
      renderWithMantine(<EditorMenu {...defaultProps} editorType='line' />);

      // The IconMoodSmile should be rendered
      const emojiButton = screen.getAllByRole('button')[0];
      expect(emojiButton).toBeInTheDocument();
    });
  });

  describe('Internationalization', () => {
    it('should handle Japanese language setting for emoji picker', () => {
      // Test that the component renders with emoji picker functionality
      renderWithMantine(
        <EditorMenu {...defaultProps} editorType='line' onEmojiSelect={mockOnEmojiSelect} />
      );

      const emojiButton = screen.getAllByRole('button')[0];
      expect(emojiButton).toBeInTheDocument();
    });

    it('should handle translation keys properly', () => {
      renderWithMantine(
        <EditorMenu {...defaultProps} showMenu={true} templates={sampleTemplates} />
      );

      // The translation key 'manageTemplates' should be displayed
      expect(screen.getByText('manageTemplates')).toBeInTheDocument();
    });
  });
});
