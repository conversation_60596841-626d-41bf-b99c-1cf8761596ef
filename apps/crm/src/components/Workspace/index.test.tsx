import { renderWithMantine } from '@/tests/utils/testUtils';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Workspace } from './index';

// Mock Tolgee modules first before any imports
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: vi.fn((key: string) => key),
  }),
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    getInitialOptions: vi.fn(() => ({
      language: 'en',
      fallbackLanguage: 'en',
      defaultNs: 'common',
      ns: ['workspace', 'common'],
      staticData: {},
      onFormatError: vi.fn((error) => error),
    })),
    t: vi.fn((key) => key),
  })),
  FormatSimple: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
  })),
  TolgeeProvider: ({ children }) => children,
  useTolgee: () => ({
    tolgee: {
      getLanguage: vi.fn(),
      changeLanguage: vi.fn(),
      t: vi.fn((key) => key),
    },
  }),
}));

// Mock API services
vi.mock('@/services/api', () => ({
  RecordAPI: {
    save: vi.fn().mockResolvedValue({ id: 'new-record-123' }),
  },
  TasksAPI: {
    exportCSV: vi.fn(),
  },
}));

// Mock utility functions
vi.mock('@/utils', () => ({
  createNewRow: vi.fn().mockReturnValue({ field1: 'test value' }),
  handleColumnOrderChange: vi.fn(),
  handleColumnSizingChange: vi.fn(),
  mapColumnsToView: vi.fn().mockReturnValue({
    id: 'view-123',
    fields: [{ id: 'field1', size: 200 }],
    fieldOrder: ['field1'],
  }),
  useShowNewRecordNotification: () => vi.fn(),
}));

// Mock all external dependencies to avoid complex context issues
vi.mock('react-router-dom', () => ({
  useParams: () => ({
    wsId: 'workspace-123',
    id: 'object-456',
    recordId: undefined,
  }),
  useNavigate: () => vi.fn(),
  Link: ({ children, to, className, target }: any) => (
    <a href={to} className={className} target={target}>
      {children}
    </a>
  ),
}));

// Mock context providers with comprehensive data
vi.mock('@/contexts/WorkspaceContext', () => ({
  WorkspaceContextProvider: ({ children }: any) => (
    <div data-testid='workspace-context-provider'>{children}</div>
  ),
  useWorkspaceContext: () => ({
    columns: [
      { id: 'field1', name: 'Field 1', type: 'text', header: 'Field 1' },
      { id: 'field2', name: 'Field 2', type: 'email', header: 'Field 2' },
    ],
    data: [
      { id: 'record1', field1: 'value1', field2: '<EMAIL>' },
      { id: 'record2', field1: 'value2', field2: '<EMAIL>' },
    ],
    activeView: {
      id: 'view-123',
      name: 'Test View',
      filters: {},
      sort: [],
      locked: false,
      rowHeight: 'md',
      permission: { 'view.read': true, 'view.export': true },
    },
    views: [
      { id: 'view-123', name: 'Test View' },
      { id: 'view-456', name: 'Another View' },
    ],
    loading: false,
    handleActionRow: vi.fn(),
    openProfile: vi.fn(),
    recordsLoading: false,
    object: {
      id: 'object-456',
      name: { singular: 'Record', plural: 'Records' },
      permission: {
        'object.read': true,
        'object.update': true,
        'view.list': true,
      },
    },
    rowSelection: {},
    setRowSelection: vi.fn(),
    selectedRowDetails: [],
    tags: [
      { id: 'tag1', name: 'Important' },
      { id: 'tag2', name: 'Urgent' },
    ],
    size: 1,
    setSize: vi.fn(),
    totalRecords: 100,
    handleViewChange: vi.fn(),
    viewLoading: false,
    viewGroups: [],
    handleApplyManageView: vi.fn(),
    onSavingCell: vi.fn(),
    textSearch: '',
    handleSearch: vi.fn(),
    objectSettings: {},
    setPinnedRecords: vi.fn(),
    mutateRecord: vi.fn(),
  }),
}));

vi.mock('@/contexts/BreadcrumbContext', () => ({
  BreadcrumbProvider: ({ children }: any) => (
    <div data-testid='breadcrumb-provider'>{children}</div>
  ),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => ({
    importLoading: false,
  }),
}));

// Mock UI components
vi.mock('@resola-ai/ui/components', () => ({
  DecaTable: ({ children, data, views, enablePermissions, enableColumnOrdering }: any) => (
    <div
      data-testid='deca-table'
      data-data-length={data?.length || 0}
      data-views-length={views?.length || 0}
      data-enable-permissions={enablePermissions}
      data-enable-column-ordering={enableColumnOrdering}
    >
      {children}
    </div>
  ),
  FieldTypes: {
    CHECKBOX: 'checkbox',
    AUTONUMBER: 'autonumber',
    CREATED_TIME: 'created_time',
    MODIFIED_TIME: 'modified_time',
    CREATED_BY: 'created_by',
    MODIFIED_BY: 'modified_by',
    TEXT: 'text',
    EMAIL: 'email',
  },
  SortOrder: {
    Ascending: 'asc',
    Descending: 'desc',
  },
}));

vi.mock('@resola-ai/ui/components/DecaTable/components/ContextMenu', () => ({
  ConfirmHeaderContextMenuItem: ({ children, ...props }: any) => (
    <div data-testid='confirm-header-context-menu-item' data-props={JSON.stringify(props)}>
      {children}
    </div>
  ),
  ConfirmRowContextMenuItem: ({ children, ...props }: any) => (
    <div data-testid='confirm-row-context-menu-item' data-props={JSON.stringify(props)}>
      {children}
    </div>
  ),
  HeaderContextMenuItem: ({ children, ...props }: any) => (
    <div data-testid='header-context-menu-item' data-props={JSON.stringify(props)}>
      {children}
    </div>
  ),
  HeaderContextMenuSeparator: () => <div data-testid='header-context-menu-separator' />,
}));

vi.mock('@resola-ai/ui/components/DecaTable/components/Toolbar', () => ({
  CustomFieldsToolbarItem: () => <div data-testid='custom-fields-toolbar-item' />,
  ExportCSVToolbarItem: ({ onExport }: any) => (
    <button data-testid='export-csv-toolbar-item' onClick={onExport}>
      Export CSV
    </button>
  ),
  MenuViewToolbarItem: ({ onApply }: any) => (
    <button data-testid='menu-view-toolbar-item' onClick={() => onApply('test')}>
      Manage View
    </button>
  ),
  SearchBoxToolbarItem: ({ onSearch, searchQuery }: any) => (
    <input
      data-testid='search-box-toolbar-item'
      value={searchQuery}
      onChange={(e) => onSearch(e.target.value)}
    />
  ),
  SelectViewToolbarItem: () => <div data-testid='select-view-toolbar-item' />,
  TableCustomFieldsChangeTypes: {
    DUPLICATE_COLUMN: 'duplicate_column',
    DELETE_COLUMN: 'delete_column',
  },
  TableFilterChangeTypes: {
    FILTER_VIEW: 'filter_view',
  },
  TableFilterToolbarItem: ({ opened, onOpenChange }: any) => (
    <button
      data-testid='table-filter-toolbar-item'
      onClick={() => onOpenChange(!opened)}
      data-opened={opened}
    >
      Filter
    </button>
  ),
  TableHeightToolbarItem: () => <div data-testid='table-height-toolbar-item' />,
  TableSelectViewChangeTypes: {
    DUPLICATE_VIEW: 'duplicate_view',
  },
  TableSortChangeTypes: {
    FILTER_VIEW: 'filter_view',
  },
  TableSortToolbarItem: ({ opened, onOpenChange }: any) => (
    <button
      data-testid='table-sort-toolbar-item'
      onClick={() => onOpenChange(!opened)}
      data-opened={opened}
    >
      Sort
    </button>
  ),
  TableAddRow: ({ onAddNewRow, disabled }: any) => (
    <button data-testid='table-add-row' onClick={onAddNewRow} disabled={disabled}>
      Add Row
    </button>
  ),
}));

vi.mock('@resola-ai/ui/components/DecaTable/constants', () => ({
  CUSTOM_SELECT_COL_ID: 'custom-select-col',
  DEFAULT_TABLE_FILTERS: {},
  RowActions: {
    DELETE_ROW: 'delete_row',
  },
  NEWCOL_ID: 'new-col-id',
}));

vi.mock('@resola-ai/ui/components/DecaTable/utils', () => ({
  PERMISSION_KEYS: {
    OBJECT_UPDATE: 'object.update',
    OBJECT_READ: 'object.read',
    VIEW_READ: 'view.read',
    VIEW_LIST: 'view.list',
    VIEW_EXPORT: 'view.export',
  },
  isPermissionAllowed: (permissions: any, key: string) => permissions[key] === true,
}));

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  Box: ({ children, className }: any) => (
    <div className={className} data-testid='box'>
      {children}
    </div>
  ),
  Center: ({ children, className }: any) => (
    <div className={className} data-testid='center'>
      {children}
    </div>
  ),
  Loader: () => <div data-testid='loader' />,
  LoadingOverlay: ({ visible, children }: any) => (
    <div data-testid='loading-overlay' data-visible={visible}>
      {children}
    </div>
  ),
  Text: ({ children, className }: any) => (
    <span className={className} data-testid='text'>
      {children}
    </span>
  ),
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: () => [false, { open: vi.fn(), close: vi.fn() }],
}));

// Mock icons
vi.mock('@tabler/icons-react', () => ({
  IconArrowLeft: () => <div data-testid='icon-arrow-left' />,
  IconArrowRight: () => <div data-testid='icon-arrow-right' />,
  IconCopy: () => <div data-testid='icon-copy' />,
  IconEdit: () => <div data-testid='icon-edit' />,
  IconEyeOff: () => <div data-testid='icon-eye-off' />,
  IconSortAscendingLetters: () => <div data-testid='icon-sort-ascending' />,
  IconSortDescendingLetters: () => <div data-testid='icon-sort-descending' />,
  IconTrash: () => <div data-testid='icon-trash' />,
}));

// Mock other components
vi.mock('../Common/MainContainer', () => ({
  default: ({ children, className }: any) => (
    <main className={className} data-testid='main-container'>
      {children}
    </main>
  ),
}));

vi.mock('../EmptyState/FilterEmptyState', () => ({
  FilterEmptyState: () => <div data-testid='filter-empty-state' />,
}));

vi.mock('../EmptyState/WorkspaceEmptyState', () => ({
  WorkspaceEmptyState: ({ clickToAdd }: any) => (
    <button data-testid='workspace-empty-state' onClick={clickToAdd}>
      Add First Column
    </button>
  ),
}));

vi.mock('../FieldSettings/AddFieldForm/AddNewColumnButton', () => ({
  default: () => <button data-testid='add-new-column-button'>Add Column</button>,
}));

vi.mock('../FieldSettings/EditFieldForm', () => ({
  default: (props: any) => (
    <div data-testid='edit-column-field' data-props={JSON.stringify(props)} />
  ),
}));

vi.mock('../MergeProfile', () => ({
  MergeProfile: ({ opened, close }: any) => (
    <div data-testid='merge-profile' data-opened={opened} onClick={close} />
  ),
}));

vi.mock('../NoPermissionAccess', () => ({
  default: ({ customText }: any) => (
    <div data-testid='no-permission-access'>{customText || 'No Permission'}</div>
  ),
}));

vi.mock('../Profile', () => ({
  default: () => <div data-testid='profile' />,
}));

vi.mock('../TableCellRendering/Cell', () => ({
  default: ({ cell, type }: any) => (
    <div data-testid='table-cell' data-type={type} data-value={cell?.getValue()}>
      {cell?.getValue()}
    </div>
  ),
}));

vi.mock('../TableCellRendering/EditCell', () => ({
  default: ({ cell }: any) => (
    <div data-testid='edit-cell' data-value={cell?.getValue()}>
      Edit: {cell?.getValue()}
    </div>
  ),
}));

vi.mock('./useObjectStyles', () => ({
  default: () => ({
    classes: {
      container: 'workspace-container',
    },
  }),
}));

vi.mock('@/constants/workspace', () => ({
  ColumnIcon: {
    text: 'text-icon',
    email: 'email-icon',
  },
  ColumnWidth: {
    text: 200,
    email: 250,
  },
}));

describe('Workspace Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Component Structure', () => {
    it('should render the Workspace component', () => {
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
    });

    it('should wrap content with WorkspaceContextProvider', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('workspace-context-provider')).toBeInTheDocument();
    });

    it('should wrap content with BreadcrumbProvider', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('breadcrumb-provider')).toBeInTheDocument();
    });

    it('should render MainContainer', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('main-container')).toBeInTheDocument();
    });

    it('should render DecaTable when permissions allow', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('deca-table')).toBeInTheDocument();
    });
  });

  describe('Permission Handling', () => {
    it('should show DecaTable when object read is allowed', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('deca-table')).toBeInTheDocument();
    });

    it('should render main workspace structure', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('deca-table')).toBeInTheDocument();
      expect(getByTestId('main-container')).toBeInTheDocument();
    });
  });

  describe('Workspace Configuration', () => {
    it('should render with correct provider hierarchy', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);

      expect(getByTestId('workspace-context-provider')).toBeInTheDocument();
      expect(getByTestId('breadcrumb-provider')).toBeInTheDocument();
      expect(getByTestId('main-container')).toBeInTheDocument();
      expect(getByTestId('deca-table')).toBeInTheDocument();
    });

    it('should pass correct data attributes to DecaTable', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      const table = getByTestId('deca-table');

      expect(table).toHaveAttribute('data-enable-permissions', 'true');
      expect(table).toHaveAttribute('data-enable-column-ordering', 'true');
      expect(table).toHaveAttribute('data-data-length', '2');
      expect(table).toHaveAttribute('data-views-length', '2');
    });

    it('should handle workspace container styling', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      const container = getByTestId('main-container');

      expect(container).toHaveClass('workspace-container');
    });
  });

  describe('Empty States', () => {
    it('should render workspace components correctly', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('deca-table')).toBeInTheDocument();
      expect(getByTestId('main-container')).toBeInTheDocument();
    });
  });

  describe('Table Components', () => {
    it('should render DecaTable with correct attributes', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      const table = getByTestId('deca-table');

      expect(table).toHaveAttribute('data-enable-permissions', 'true');
      expect(table).toHaveAttribute('data-enable-column-ordering', 'true');
    });

    it('should render table with proper data structure', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      const table = getByTestId('deca-table');

      expect(table).toHaveAttribute('data-data-length', '2');
      expect(table).toHaveAttribute('data-views-length', '2');
    });
  });

  describe('Context Menu and Configuration', () => {
    it('should render DecaTable with context menus available', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('deca-table')).toBeInTheDocument();
    });

    it('should pass basic configuration to DecaTable', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      const table = getByTestId('deca-table');

      expect(table).toHaveAttribute('data-enable-permissions', 'true');
      expect(table).toHaveAttribute('data-enable-column-ordering', 'true');
      expect(table).toHaveAttribute('data-data-length', '2');
      expect(table).toHaveAttribute('data-views-length', '2');
    });
  });

  describe('Component Integration', () => {
    it('should integrate with context providers', () => {
      const { container } = renderWithMantine(<Workspace />);
      expect(container.firstChild).toBeInTheDocument();
    });

    it('should handle component lifecycle', () => {
      const { unmount } = renderWithMantine(<Workspace />);
      expect(() => unmount()).not.toThrow();
    });

    it('should render consistently across multiple calls', () => {
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
    });
  });

  describe('Error Boundaries and Edge Cases', () => {
    it('should handle rendering without crashing', () => {
      expect(() => {
        renderWithMantine(<Workspace />);
      }).not.toThrow();
    });

    it('should render all main components', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      expect(getByTestId('deca-table')).toBeInTheDocument();
      expect(getByTestId('workspace-context-provider')).toBeInTheDocument();
      expect(getByTestId('breadcrumb-provider')).toBeInTheDocument();
    });
  });

  describe('Export and Component Props', () => {
    it('should export the Workspace component', () => {
      expect(Workspace).toBeDefined();
      expect(typeof Workspace).toBe('function');
    });

    it('should be a valid React component', () => {
      expect(typeof Workspace).toBe('function');
      expect(Workspace.name).toBe('Workspace');
    });

    it('should render with default configuration', () => {
      const { container } = renderWithMantine(<Workspace />);
      expect(container.firstChild).toBeTruthy();
    });

    it('should maintain component structure', () => {
      const { container } = renderWithMantine(<Workspace />);
      expect(container).toBeInTheDocument();
    });
  });

  describe('Advanced Functionality', () => {
    it('should render functional workspace with all providers', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);

      expect(getByTestId('deca-table')).toBeInTheDocument();
      expect(getByTestId('workspace-context-provider')).toBeInTheDocument();
      expect(getByTestId('breadcrumb-provider')).toBeInTheDocument();
      expect(getByTestId('main-container')).toBeInTheDocument();
    });

    it('should maintain proper component hierarchy', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);

      const workspaceProvider = getByTestId('workspace-context-provider');
      const breadcrumbProvider = getByTestId('breadcrumb-provider');
      const mainContainer = getByTestId('main-container');
      const decaTable = getByTestId('deca-table');

      expect(workspaceProvider).toContainElement(breadcrumbProvider);
      expect(breadcrumbProvider).toContainElement(mainContainer);
      expect(mainContainer).toContainElement(decaTable);
    });

    it('should handle component state and configuration correctly', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      const table = getByTestId('deca-table');

      expect(table).toHaveAttribute('data-enable-permissions', 'true');
      expect(table).toHaveAttribute('data-enable-column-ordering', 'true');
      expect(table).toHaveAttribute('data-data-length', '2');
    });
  });

  describe('State Management and Mocking', () => {
    it('should properly mock external dependencies', () => {
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
    });

    it('should handle context dependencies', () => {
      const { container } = renderWithMantine(<Workspace />);
      expect(container).toBeDefined();
    });

    it('should render table with correct data attributes', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);
      const table = getByTestId('deca-table');

      expect(table).toHaveAttribute('data-data-length', '2');
      expect(table).toHaveAttribute('data-views-length', '2');
    });

    it('should verify component structure integrity', () => {
      const { getByTestId } = renderWithMantine(<Workspace />);

      expect(getByTestId('workspace-context-provider')).toBeInTheDocument();
      expect(getByTestId('breadcrumb-provider')).toBeInTheDocument();
      expect(getByTestId('main-container')).toBeInTheDocument();
      expect(getByTestId('deca-table')).toBeInTheDocument();
    });
  });
});
