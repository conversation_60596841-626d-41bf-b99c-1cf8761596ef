import { renderWithMantine } from '@/tests/utils/testUtils';
import { describe, expect, it, vi } from 'vitest';
import type { FieldOptions } from '@/models';

// Mock all complex dependencies to focus on core functionality
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => ({
    handleUpdateColumn: vi.fn(),
    object: {
      permission: {
        'object.update': true,
      },
    },
  }),
}));

vi.mock('@/utils', () => ({
  handleDragOption: vi.fn(() => []),
}));

// Mock the OptionsMenu component itself to avoid complex Menu context issues
vi.mock('./OptionsMenu', () => ({
  OptionsMenu: ({
    config,
    onUpdateCell,
  }: { config?: FieldOptions; onUpdateCell: (value: string) => void }) => (
    <div data-testid='options-menu'>
      <input data-testid='search-input' placeholder='Search or add an option' />
      <div data-testid='menu-dropdown'>
        {config?.choices?.map((choice) => (
          <div key={choice.id} data-testid='option-item' onClick={() => onUpdateCell(choice.id)}>
            {choice.label}
          </div>
        ))}
      </div>
    </div>
  ),
}));

import { OptionsMenu } from './OptionsMenu';

describe('OptionsMenu', () => {
  const mockConfig = {
    choices: [
      { id: '1', label: 'Option 1', color: 'blue', time: 1000 },
      { id: '2', label: 'Option 2', color: 'red', time: 2000 },
      { id: '3', label: 'Option 3', color: 'green', time: 3000 },
    ],
  };
  const mockOnUpdateCell = vi.fn();

  it('should render without errors', () => {
    const { getByTestId } = renderWithMantine(
      <OptionsMenu colId='test-column' config={mockConfig} onUpdateCell={mockOnUpdateCell} />
    );

    expect(getByTestId('options-menu')).toBeInTheDocument();
    expect(getByTestId('search-input')).toBeInTheDocument();
    expect(getByTestId('menu-dropdown')).toBeInTheDocument();
  });

  it('should render options from config', () => {
    const { getAllByTestId } = renderWithMantine(
      <OptionsMenu colId='test-column' config={mockConfig} onUpdateCell={mockOnUpdateCell} />
    );

    const options = getAllByTestId('option-item');
    expect(options).toHaveLength(3);
  });

  it('should handle undefined config', () => {
    const { getByTestId } = renderWithMantine(
      <OptionsMenu colId='test-column' config={undefined} onUpdateCell={mockOnUpdateCell} />
    );

    expect(getByTestId('options-menu')).toBeInTheDocument();
  });
});
