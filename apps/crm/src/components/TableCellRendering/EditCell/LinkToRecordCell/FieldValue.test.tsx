import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import type { ObjectColumn } from '@/models';
import { FieldTypes } from '@resola-ai/ui/components';
import FieldValue from './FieldValue';

// Mock the UI components
vi.mock('@resola-ai/ui', () => ({
  DecaCheckbox: vi.fn(({ checked, readOnly }) => (
    <input type='checkbox' checked={checked} readOnly={readOnly} data-testid='deca-checkbox' />
  )),
}));

vi.mock('../../Cell/SingleSelectCell', () => ({
  SingleSelectCell: vi.fn(({ value, choices }) => (
    <div data-testid='single-select-cell' data-value={value} data-choices={JSON.stringify(choices)}>
      Single Select: {value}
    </div>
  )),
}));

vi.mock('../../Cell/MultiSelectCell', () => ({
  MultiSelectCell: vi.fn(({ value, choices, showRemove }) => (
    <div
      data-testid='multi-select-cell'
      data-value={JSON.stringify(value)}
      data-choices={JSON.stringify(choices)}
      data-show-remove={showRemove}
    >
      Multi Select: {JSON.stringify(value)}
    </div>
  )),
}));

const createMockField = (overrides: Partial<ObjectColumn> = {}): ObjectColumn =>
  ({
    id: 'test-field',
    name: 'Test Field',
    header: 'Test Field',
    type: FieldTypes.SINGLE_LINE_TEXT,
    accessorKey: 'test-field',
    size: 200,
    enableSorting: true,
    enableColumnFilter: true,
    ...overrides,
  }) as ObjectColumn;

describe('FieldValue', () => {
  describe('CHECKBOX field type', () => {
    it('should render DecaCheckbox for checkbox field type with true value', () => {
      const field = createMockField({
        type: FieldTypes.CHECKBOX,
      });

      const { getByTestId } = renderWithMantine(<FieldValue field={field} value={true} />);

      const checkbox = getByTestId('deca-checkbox');
      expect(checkbox).toBeInTheDocument();
      expect(checkbox).toBeChecked();
    });

    it('should render DecaCheckbox for checkbox field type with false value', () => {
      const field = createMockField({
        type: FieldTypes.CHECKBOX,
      });

      const { getByTestId } = renderWithMantine(<FieldValue field={field} value={false} />);

      const checkbox = getByTestId('deca-checkbox');
      expect(checkbox).toBeInTheDocument();
      expect(checkbox).not.toBeChecked();
    });
  });

  describe('SINGLE_SELECT field type', () => {
    it('should render SingleSelectCell with choices from field options', () => {
      const choices = [
        { id: '1', label: 'Option 1', color: 'blue' },
        { id: '2', label: 'Option 2', color: 'red' },
      ];

      const field = createMockField({
        type: FieldTypes.SINGLE_SELECT,
        options: { choices },
      });

      const { getByTestId } = renderWithMantine(<FieldValue field={field} value='1' />);

      const singleSelect = getByTestId('single-select-cell');
      expect(singleSelect).toBeInTheDocument();
      expect(singleSelect).toHaveAttribute('data-value', '1');
      expect(singleSelect).toHaveAttribute('data-choices', JSON.stringify(choices));
    });

    it('should render SingleSelectCell with empty choices when field options not provided', () => {
      const field = createMockField({
        type: FieldTypes.SINGLE_SELECT,
      });

      const { getByTestId } = renderWithMantine(<FieldValue field={field} value='1' />);

      const singleSelect = getByTestId('single-select-cell');
      expect(singleSelect).toBeInTheDocument();
      expect(singleSelect).toHaveAttribute('data-choices', '[]');
    });
  });

  describe('IMAGE field type', () => {
    it('should render Avatar for image field type', () => {
      const field = createMockField({
        type: FieldTypes.IMAGE,
      });

      const imageUrl = 'https://example.com/image.jpg';
      const { container } = renderWithMantine(<FieldValue field={field} value={imageUrl} />);

      const avatar = container.querySelector('img');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', imageUrl);
      expect(avatar).toHaveAttribute('alt', 'avatar');
    });
  });

  describe('CREATED_BY and MODIFIED_BY field types', () => {
    it('should render Avatar for CREATED_BY field type with user picture', () => {
      const field = createMockField({
        type: FieldTypes.CREATED_BY,
      });

      const userValue = { picture: 'https://example.com/user.jpg', name: 'John Doe' };
      const { container } = renderWithMantine(<FieldValue field={field} value={userValue} />);

      const avatar = container.querySelector('img');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', userValue.picture);
      expect(avatar).toHaveAttribute('alt', 'avatar');
    });

    it('should render Avatar for MODIFIED_BY field type with user picture', () => {
      const field = createMockField({
        type: FieldTypes.MODIFIED_BY,
      });

      const userValue = { picture: 'https://example.com/user.jpg', name: 'Jane Doe' };
      const { container } = renderWithMantine(<FieldValue field={field} value={userValue} />);

      const avatar = container.querySelector('img');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', userValue.picture);
    });
  });

  describe('MULTI_SELECT field type', () => {
    it('should render MultiSelectCell with choices and showRemove false', () => {
      const choices = [
        { id: '1', label: 'Tag 1', color: 'blue' },
        { id: '2', label: 'Tag 2', color: 'red' },
      ];

      const field = createMockField({
        type: FieldTypes.MULTI_SELECT,
        options: { choices },
      });

      const selectedValues = ['1', '2'];
      const { getByTestId } = renderWithMantine(
        <FieldValue field={field} value={selectedValues} />
      );

      const multiSelect = getByTestId('multi-select-cell');
      expect(multiSelect).toBeInTheDocument();
      expect(multiSelect).toHaveAttribute('data-value', JSON.stringify(selectedValues));
      expect(multiSelect).toHaveAttribute('data-choices', JSON.stringify(choices));
      expect(multiSelect).toHaveAttribute('data-show-remove', 'false');
    });

    it('should render MultiSelectCell with empty array when value is undefined', () => {
      const field = createMockField({
        type: FieldTypes.MULTI_SELECT,
        options: { choices: [] },
      });

      const { getByTestId } = renderWithMantine(<FieldValue field={field} value={undefined} />);

      const multiSelect = getByTestId('multi-select-cell');
      expect(multiSelect).toHaveAttribute('data-value', '[]');
    });
  });

  describe('RELATIONSHIP field type', () => {
    it('should render recordId when value is object with recordId', () => {
      const field = createMockField({
        type: FieldTypes.RELATIONSHIP,
      });

      const relationshipValue = { recordId: 'rec123', displayName: 'Related Record' };
      const { container } = renderWithMantine(
        <FieldValue field={field} value={relationshipValue} />
      );

      expect(container.textContent).toContain('rec123');
    });

    it('should render null when value is empty object', () => {
      const field = createMockField({
        type: FieldTypes.RELATIONSHIP,
      });

      const { container } = renderWithMantine(<FieldValue field={field} value={{}} />);

      // Should not render any specific content for empty object - check that no text content elements exist
      const textElements = container.querySelectorAll('*:not(style):not(head):not(title)');
      const hasTextContent = Array.from(textElements).some(
        (el) => el.textContent && el.textContent.trim() && !el.textContent.includes(':root{')
      );
      expect(hasTextContent).toBe(false);
    });

    it('should render null when value is null', () => {
      const field = createMockField({
        type: FieldTypes.RELATIONSHIP,
      });

      const { container } = renderWithMantine(<FieldValue field={field} value={null} />);

      // Should not render any specific content for null value - check that no text content elements exist
      const textElements = container.querySelectorAll('*:not(style):not(head):not(title)');
      const hasTextContent = Array.from(textElements).some(
        (el) => el.textContent && el.textContent.trim() && !el.textContent.includes(':root{')
      );
      expect(hasTextContent).toBe(false);
    });
  });

  describe('Default field type handling', () => {
    it('should render text value for unknown field type', () => {
      const field = createMockField({
        type: 'CUSTOM_TYPE' as any,
      });

      const textValue = 'Some text content';
      renderWithMantine(<FieldValue field={field} value={textValue} />);

      // Should render the text value as content
      expect(screen.getByText(textValue)).toBeInTheDocument();
    });

    it('should render text value for SINGLE_LINE_TEXT field type', () => {
      const field = createMockField({
        type: FieldTypes.SINGLE_LINE_TEXT,
      });

      const textValue = 'Some text content';
      renderWithMantine(<FieldValue field={field} value={textValue} />);

      // Should render the text value as content
      expect(screen.getByText(textValue)).toBeInTheDocument();
    });

    it('should render number value for NUMBER field type', () => {
      const field = createMockField({
        type: FieldTypes.NUMBER,
      });

      const numberValue = 42;
      renderWithMantine(<FieldValue field={field} value={numberValue} />);

      // Should render the number value as text content
      expect(screen.getByText('42')).toBeInTheDocument();
    });

    it('should render long text value for LONG_TEXT field type', () => {
      const field = createMockField({
        type: FieldTypes.LONG_TEXT,
      });

      const longTextValue = 'This is a long text content';
      renderWithMantine(<FieldValue field={field} value={longTextValue} />);

      // Should render the long text value as content
      expect(screen.getByText(longTextValue)).toBeInTheDocument();
    });
  });

  describe('Edge cases', () => {
    it('should handle undefined field gracefully', () => {
      renderWithMantine(<FieldValue field={undefined} value='test' />);

      // Should render the value as text when field is undefined
      expect(screen.getByText('test')).toBeInTheDocument();
    });

    it('should handle undefined value gracefully', () => {
      const field = createMockField({
        type: FieldTypes.SINGLE_LINE_TEXT,
      });

      const { container } = renderWithMantine(<FieldValue field={field} value={undefined} />);

      expect(container).toBeInTheDocument();
    });

    it('should handle null value gracefully', () => {
      const field = createMockField({
        type: FieldTypes.SINGLE_LINE_TEXT,
      });

      const { container } = renderWithMantine(<FieldValue field={field} value={null} />);

      expect(container).toBeInTheDocument();
    });

    it('should handle empty string value', () => {
      const field = createMockField({
        type: FieldTypes.SINGLE_LINE_TEXT,
      });

      const { container } = renderWithMantine(<FieldValue field={field} value='' />);

      // Should not render visible content for empty string - check that no meaningful text content exists
      const textElements = container.querySelectorAll('*:not(style):not(head):not(title)');
      const hasTextContent = Array.from(textElements).some(
        (el) => el.textContent && el.textContent.trim() && !el.textContent.includes(':root{')
      );
      expect(hasTextContent).toBe(false);
    });
  });

  describe('Component memoization', () => {
    it('should be wrapped with React.memo', () => {
      // React.memo components may be objects or functions, just verify it's defined and renderable
      expect(FieldValue).toBeDefined();
      expect(typeof FieldValue === 'function' || typeof FieldValue === 'object').toBe(true);
    });
  });
});
