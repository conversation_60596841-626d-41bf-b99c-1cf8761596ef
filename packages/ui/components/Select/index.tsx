import { ActionIcon, Box, Flex, Menu, Text, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown, IconChevronUp, IconX } from '@tabler/icons-react';
import debounce from 'lodash/debounce';
import React, { BaseSyntheticEvent, useCallback, useState } from 'react';

export type Option = {
  value: string | number;
  label: string | React.ReactNode;
  selectedDisplay?: string | React.ReactNode;
  filterList?: string[];
  [key: string]: any;
};
export type Group = { label?: string; value?: string; options: Option[] };
export type SelectProps = {
  defaultValue?: string | number;
  className?: string;
  search?: boolean;
  placeholder?: string;
  optionsClassName?: string;
  groups: Group[];
  onChange?: (value: string | number) => void;
  error?: string | boolean;
  width?: string;
};

const useStyle = createStyles((theme, { error = false }: { error: boolean }) => ({
  container: {
    color: theme.colors.decaGrey[9],
  },
  target: {
    justifyContent: 'space-between',
    alignItems: 'center',
    height: rem(38),
    padding: `${rem(8)} ${rem(12)}`,
    cursor: 'pointer',
    borderRadius: theme.radius.sm,
    // TODO update color
    border: `1px solid ${error ? '#fa5252' : theme.colors.decaGrey[0]}`,
  },
  value: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  search: {
    '& input': {
      outline: 'none',
      border: 'none',
    },
  },
  item: {
    cursor: 'pointer',
    borderRadius: theme.radius.sm,

    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
    },
  },
  selected: {
    backgroundColor: theme.colors.decaLight[1],
  },
  options: {
    overflow: 'auto',
    '&::-webkit-scrollbar': {
      width: rem(4),
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: theme.colors.decaLight[1],
      borderRadius: rem(4),
    },
  },
}));

export const Select = ({
  defaultValue,
  className,
  search: showSearch = false,
  placeholder,
  onChange,
  optionsClassName,
  groups: initialGroups,
  error,
  width = rem(290),
}: SelectProps) => {
  const { classes, cx } = useStyle({ error: !!error });
  const [selected, setSelected] = useState<Option>();
  const [search, setSearch] = useState<string>('');
  const [opened, setOpened] = useState(false);
  const [groups, setGroups] = useState(initialGroups);

  React.useEffect(() => {
    if (initialGroups) {
      setGroups(initialGroups);
    }
  }, [initialGroups]);

  React.useEffect(() => {
    if (defaultValue || defaultValue === 0) {
      const _selected = initialGroups
        .map(group => group.options)
        .flat()
        .find(option => option.value === defaultValue);
      setSelected(_selected);
    } else {
      setSelected(undefined);
    }
  }, [defaultValue, initialGroups]);

  const debounceSearch = debounce((_value: string) => {
    const _val = _value.trim().toUpperCase();
    const filteredGroups = initialGroups.map(group => {
      const filteredOptions = group.options.filter(option => {
        return option.filterList?.some(filter => filter.toUpperCase().includes(_val.toUpperCase()));
      });

      return {
        ...group,
        options: filteredOptions,
      };
    });
    setGroups(filteredGroups);
  }, 300);

  const handleSearch = useCallback(
    (val: string) => {
      setSearch(val);
      return debounceSearch(val);
    },
    [debounceSearch]
  );

  const clearSearch = (e: BaseSyntheticEvent) => {
    e.stopPropagation();
    handleSearch('');
  };

  return (
    <Box className={cx(classes.container, className)}>
      <Menu shadow='md' width={width} zIndex={9999}>
        <Menu.Target>
          <Flex className={classes.target} mb={rem(8)} onClick={() => setOpened(!opened)}>
            <Flex gap={rem(10)} align={'center'} c={error ? 'decaRed.5' : 'decaGrey.9'}>
              {selected?.selectedDisplay || selected?.label}
            </Flex>
            <ActionIcon variant='white' c='decaGrey.5' size={rem(20)}>
              {opened ? <IconChevronUp /> : <IconChevronDown />}
            </ActionIcon>
          </Flex>
        </Menu.Target>

        <Menu.Dropdown>
          <Box>
            {showSearch && (
              <TextInput
                placeholder={placeholder || 'Find'}
                value={search}
                className={classes.search}
                onChange={e => handleSearch(e.target.value)}
                rightSection={
                  search && (
                    <IconX cursor={'pointer'} style={{ width: rem(16) }} onClick={clearSearch} />
                  )
                }
              />
            )}
            <Flex
              direction={'column'}
              mah={rem(280)}
              className={cx(classes.options, optionsClassName)}>
              {groups.map((group, index) => (
                <Box key={index}>
                  {!!group.options.length && (
                    <Text px={rem(12)} c={'decaNavy.4'} fw={600}>
                      {group.label}
                    </Text>
                  )}
                  {group.options.map(option => (
                    <Menu.Item
                      key={option.value}
                      className={cx({
                        [classes.selected]: option.value === selected?.value,
                      })}
                      onClick={() => {
                        setSelected(option);
                        onChange?.(option.value);
                        setOpened(false);
                      }}>
                      <Flex align={'center'} gap={rem(6)}>
                        {option.label}
                      </Flex>
                    </Menu.Item>
                  ))}
                </Box>
              ))}
            </Flex>
          </Box>
        </Menu.Dropdown>
      </Menu>
      {error && (
        <Box
          sx={{
            wordBreak: 'break-word',
            color: '#fa5252',
            fontSize: rem(10),
            marginTop: rem(-8),
          }}>
          {error}
        </Box>
      )}
    </Box>
  );
};
